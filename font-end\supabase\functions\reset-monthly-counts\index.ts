
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";
import Stripe from "https://esm.sh/stripe@14.21.0";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log('[RESET-MONTHLY-COUNTS] Starting monthly reset process with Stripe billing cycles');

    const stripeKey = Deno.env.get("STRIPE_SECRET_KEY");
    if (!stripeKey) {
      console.error('[RESET-MONTHLY-COUNTS] STRIPE_SECRET_KEY is not set');
      throw new Error("STRIPE_SECRET_KEY is not set");
    }

    const stripe = new Stripe(stripeKey, { apiVersion: "2023-10-16" });

    // Get all users with Stripe subscription IDs whose billing cycles might need reset
    const { data: usersWithSubscriptions, error: usersError } = await supabase
      .from('user_details')
      .select('id, email, stripe_subscription_id, current_billing_cycle_end, monthly_tutorials_created')
      .not('stripe_subscription_id', 'is', null);

    if (usersError) {
      console.error('[RESET-MONTHLY-COUNTS] Error fetching users:', usersError);
      throw usersError;
    }

    console.log(`[RESET-MONTHLY-COUNTS] Found ${usersWithSubscriptions?.length || 0} users with subscriptions`);

    let updatedUsersCount = 0;

    // Process each user with a Stripe subscription
    if (usersWithSubscriptions) {
      for (const user of usersWithSubscriptions) {
        try {
          // Get current subscription from Stripe
          const subscription = await stripe.subscriptions.retrieve(user.stripe_subscription_id);
          
          if (subscription.status === 'active' || subscription.status === 'trialing') {
            const currentPeriodStart = new Date(subscription.current_period_start * 1000);
            const currentPeriodEnd = new Date(subscription.current_period_end * 1000);
            const now = new Date();

            // Check if the billing cycle has progressed since our last update
            const storedBillingEnd = user.current_billing_cycle_end ? new Date(user.current_billing_cycle_end) : null;
            
            if (!storedBillingEnd || currentPeriodEnd > storedBillingEnd) {
              console.log(`[RESET-MONTHLY-COUNTS] Updating billing cycle for user ${user.id}`);
              
              // Update billing cycle and reset count if cycle has changed
              const shouldResetCount = storedBillingEnd && currentPeriodEnd > storedBillingEnd;
              
              const { error: updateError } = await supabase.rpc('update_user_billing_cycle', {
                p_user_id: user.id,
                p_stripe_subscription_id: user.stripe_subscription_id,
                p_current_period_start: currentPeriodStart.toISOString(),
                p_current_period_end: currentPeriodEnd.toISOString()
              });

              if (updateError) {
                console.error(`[RESET-MONTHLY-COUNTS] Error updating billing cycle for user ${user.id}:`, updateError);
              } else {
                // Reset monthly count if we've moved to a new billing cycle
                if (shouldResetCount) {
                  const { error: resetError } = await supabase
                    .from('user_details')
                    .update({ monthly_tutorials_created: 0 })
                    .eq('id', user.id);
                  
                  if (resetError) {
                    console.error(`[RESET-MONTHLY-COUNTS] Error resetting count for user ${user.id}:`, resetError);
                  } else {
                    console.log(`[RESET-MONTHLY-COUNTS] Reset count for user ${user.id} (was ${user.monthly_tutorials_created})`);
                  }
                }
                updatedUsersCount++;
              }
            }
          } else {
            console.log(`[RESET-MONTHLY-COUNTS] Subscription ${user.stripe_subscription_id} is ${subscription.status}, skipping`);
          }
        } catch (subscriptionError) {
          console.error(`[RESET-MONTHLY-COUNTS] Error processing subscription for user ${user.id}:`, subscriptionError);
        }
      }
    }

    // Also handle users without Stripe subscriptions (free tier users)
    // These users get monthly resets based on calendar months
    const { data, error } = await supabase.rpc('reset_monthly_tutorial_counts');
    
    if (error) {
      console.error('[RESET-MONTHLY-COUNTS] Database error during free tier reset:', error);
      throw error;
    }

    console.log(`[RESET-MONTHLY-COUNTS] Reset process completed. Updated ${updatedUsersCount} subscription users.`);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Monthly tutorial counts reset successfully with Stripe billing cycles',
        updated_subscription_users: updatedUsersCount,
        timestamp: new Date().toISOString()
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );

  } catch (error) {
    console.error('[RESET-MONTHLY-COUNTS] Error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
});
