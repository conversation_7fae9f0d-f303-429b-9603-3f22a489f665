
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";

export function CTASection() {
  return (
    <section className="py-24 bg-gradient-to-r from-tutorial-primary to-tutorial-secondary relative overflow-hidden">
      <div className="absolute inset-0 bg-black/10"></div>
      <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-10 right-10 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      
      <div className="container mx-auto px-4 text-center relative">
        <div className="max-w-4xl mx-auto">
          <div className="inline-flex items-center bg-white/20 rounded-full px-6 py-3 mb-8 backdrop-blur-sm">
            <i className="fa-solid fa-rocket text-yellow-300 mr-2"></i>
            <span className="text-white font-semibold">LIMITED TIME: 7-DAY FREE TRIAL</span>
          </div>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white leading-tight">
            Stop Writing Documentation.
            <span className="block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
              Start Generating It.
            </span>
          </h2>
          
          <p className="text-xl md:text-2xl mb-10 max-w-3xl mx-auto text-white/95 leading-relaxed">
            Join 15,000+ developers who've already saved thousands of hours with AI-powered 
            documentation. Transform your repository into a tutorial in under 5 minutes.
          </p>
          
          <div className="flex flex-col sm:flex-row justify-center gap-6 mb-12">
            <Link to="/dashboard">
              <Button
                size="lg"
                className="bg-white text-tutorial-primary hover:bg-gray-100 text-xl px-10 py-5 shadow-2xl font-bold"
              >
                Start Free Trial Now
                <i className="fa-solid fa-arrow-right ml-3"></i>
              </Button>
            </Link>
            <Link to="/public-gallery">
              <Button
                size="lg"
                variant="outline"
                className="border-2 border-white text-white hover:bg-white/10 text-xl px-10 py-5 backdrop-blur-sm font-bold"
              >
                <i className="fa-solid fa-play mr-3"></i>
                See Live Examples
              </Button>
            </Link>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <i className="fa-solid fa-clock text-3xl text-yellow-300 mb-3"></i>
              <div className="text-white font-semibold mb-2">Save 90% Time</div>
              <div className="text-white/80 text-sm">From weeks to minutes</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <i className="fa-solid fa-credit-card text-3xl text-red-300 mb-3 line-through"></i>
              <div className="text-white font-semibold mb-2">No Credit Card</div>
              <div className="text-white/80 text-sm">Start completely free</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <i className="fa-solid fa-infinity text-3xl text-blue-300 mb-3"></i>
              <div className="text-white font-semibold mb-2">Cancel Anytime</div>
              <div className="text-white/80 text-sm">No long-term commitment</div>
            </div>
          </div>
          
          <p className="text-white/70 mt-8 text-sm">
            Trusted by developers at Google, Microsoft, GitHub, Meta, and 500+ other companies
          </p>
        </div>
      </div>
    </section>
  );
}
