@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');


@tailwind base;
@tailwind components;
@tailwind utilities;



@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 252 56% 57%;
    --primary-foreground: 210 40% 98%;

    --secondary: 250 43% 48%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 250 95% 64%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 252 56% 57%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 252 56% 57%;
    --primary-foreground: 210 40% 98%;

    --secondary: 250 43% 48%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 250 95% 64%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}



@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;

    /* Performance optimizations (NEW)*/
    font-display: swap;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

  }


/*NEW*/



  /* SEO and accessibility improvements */
  html {
    scroll-behavior: smooth;
  }

  /* Skip link for accessibility */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }

  /* Focus indicators for accessibility */
  *:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Performance: GPU acceleration for animations */
  .will-change-transform {
    will-change: transform;
  }

  .will-change-opacity {
    will-change: opacity;
  }

  /* Optimize images for performance */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Prevent layout shift */
  .aspect-ratio-16-9 {
    aspect-ratio: 16 / 9;
  }

  .aspect-ratio-4-3 {
    aspect-ratio: 4 / 3;
  }

  .aspect-ratio-1-1 {
    aspect-ratio: 1 / 1;
  }



/*OLD*/

  .tutorial-gradient {
    @apply bg-gradient-to-r from-tutorial-primary to-tutorial-secondary text-white;
  }

  .tutorial-tag {
    @apply inline-flex items-center rounded-full bg-tutorial-light text-tutorial-primary px-2.5 py-0.5 text-xs font-medium;
  }

  .step-number {
    @apply flex items-center justify-center w-7 h-7 rounded-full tutorial-gradient text-white font-semibold text-sm;
  }
}

/*NEW*/

@layer utilities {
  /* Enhanced animations for hero section */
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-float-slow {
    animation: float-slow 6s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 3s ease-in-out infinite;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .animate-glow,
    .animate-float-slow,
    .animate-pulse-glow,
    .animate-pulse-slow,
    .animate-float,
    .animate-fade-in,
    .animate-slide-up {
      animation: none;
    }
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);
  }
  to {
    box-shadow: 0 0 30px rgba(168, 85, 247, 0.8);
  }
}

@keyframes float-slow {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
    box-shadow: 0 0 40px rgba(168, 85, 247, 0.6);
  }
}



/*  OLD */


body {
  font-family: 'Inter', sans-serif;
}

/* Fix for markdown overflow */
.prose {
  max-width: 100% !important;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}

.prose pre {
  max-width: 100%;
  overflow-x: auto;
}

.prose table {
  display: block;
  max-width: 100%;
  overflow-x: auto;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Enhanced animations and effects */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth transitions for interactive elements */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced backdrop blur effect */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Custom scrollbar styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

/* Enhanced prose styling for better readability */
.prose-enhanced {
  line-height: 1.8;
  color: #374151;
}

.prose-enhanced h1,
.prose-enhanced h2,
.prose-enhanced h3,
.prose-enhanced h4,
.prose-enhanced h5,
.prose-enhanced h6 {
  color: #111827;
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose-enhanced p {
  margin-bottom: 1.25rem;
}

.prose-enhanced code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  color: #dc2626;
}

.prose-enhanced pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.75rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.prose-enhanced blockquote {
  border-left: 4px solid #6366f1;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: #6b7280;
}
