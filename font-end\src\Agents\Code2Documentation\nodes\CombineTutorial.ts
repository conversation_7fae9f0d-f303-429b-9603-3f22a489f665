import { Node } from "../../../pocketflow";
import { SharedStore } from "../types";
import { emitGraphStatus, emitProgress, emitComplete } from "../utils/events";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { BUCKET_NAME } from "@/constants";
import { callLlm_openrouter } from "@/Agents/shared/callLlm_openrouter";
import { buildPrompt } from "../../../pocketflow/utils/buildPrompt";
import { PROJECT_SUMMARY_PROMPT } from "../prompts/projectSummary";

// Detect environment
const isNode = typeof window === 'undefined';

export class CombineTutorial extends Node<SharedStore> {

  async prep(shared: SharedStore) {
    // Emit graph status to indicate this node is starting
    emitGraphStatus("CombineTutorial", 0, "Starting tutorial compilation");

    const projectName = shared.project_name!;
    const outputBaseDir = shared.output_dir || "output"; // Default output dir
    const outputPath = `${outputBaseDir}/${projectName}`;
    const repoUrl = shared.repo_url;
    
   

    emitGraphStatus("CombineTutorial", 10, "Gathering tutorial components");

    // Get potentially translated data
    const relationshipsData = shared.relationships!;
    const chapterOrder = shared.chapter_order || [];
    const abstractions = shared.abstractions || [];
    const chaptersContent = shared.chapters || [];

    // --- Generate Mermaid Diagram ---
    emitGraphStatus("CombineTutorial", 20, "Generating relationship diagram");

    const mermaidLines = ["flowchart TD"];
    // Add nodes for each abstraction using potentially translated names
    for (let i = 0; i < abstractions.length; i++) {
      const nodeId = `A${i}`;
      // Use potentially translated name, sanitize for Mermaid ID and label
      const sanitizedName = abstractions[i].name.replace(/"/g, "");
      const nodeLabel = sanitizedName; // Using sanitized name only
      mermaidLines.push(`    ${nodeId}["${nodeLabel}"]`); // Node label uses potentially translated name
    }

    // Add edges for relationships using potentially translated labels
    for (const rel of relationshipsData.details) {
      const fromNodeId = `A${rel.from}`;
      const toNodeId = `A${rel.to}`;
      // Use potentially translated label, sanitize
      let edgeLabel = rel.label.replace(/"/g, "").replace(/\n/g, " "); // Basic sanitization
      const maxLabelLen = 30;
      if (edgeLabel.length > maxLabelLen) {
        edgeLabel = edgeLabel.substring(0, maxLabelLen - 3) + "...";
      }
      mermaidLines.push(`    ${fromNodeId} -- "${edgeLabel}" --> ${toNodeId}`); // Edge label uses potentially translated label
    }

    const mermaidDiagram = mermaidLines.join("\n");
    // --- End Mermaid ---

    // --- Prepare index.md content ---
    emitGraphStatus("CombineTutorial", 30, "Creating index page");

    let indexContent = `# Tutorial: ${projectName}\n\n`;
    indexContent += `${relationshipsData.summary}\n\n`; // Use the potentially translated summary directly
    // Keep fixed strings in English
    if (repoUrl) {
      indexContent += `**Source Repository:** [${repoUrl}](${repoUrl})\n\n`;
    }

    // Add Mermaid diagram for relationships (diagram itself uses potentially translated names/labels)
    indexContent += "```mermaid\n";
    indexContent += mermaidDiagram + "\n";
    indexContent += "```\n\n";

    // Keep fixed strings in English
    indexContent += `## Chapters\n\n`;

    emitGraphStatus("CombineTutorial", 40, "Preparing chapter files");

    const chapterFiles = [];
    // Generate chapter links based on the determined order, using potentially translated names
    for (let i = 0; i < chapterOrder.length; i++) {
      const abstractionIndex = chapterOrder[i];
      // Ensure index is valid and we have content for it
      if (
        0 <= abstractionIndex &&
        abstractionIndex < abstractions.length &&
        i < chaptersContent.length
      ) {
        const abstractionName = abstractions[abstractionIndex].name; // Potentially translated name
        // Sanitize potentially translated name for filename
        const safeName = abstractionName
          .replace(/[^a-zA-Z0-9]/g, "_")
          .toLowerCase();
        const filename = `${(i + 1)
          .toString()
          .padStart(2, "0")}_${safeName}.md`;
        indexContent += `${i + 1}. [${abstractionName}](${filename})\n`; // Use potentially translated name in link text

        // Add attribution to chapter content (using English fixed string)
        let chapterContent = chaptersContent[i]; // Potentially translated content
        if (!chapterContent.endsWith("\n\n")) {
          chapterContent += "\n\n";
        }
        // Keep fixed strings in English
        chapterContent += `---\n\nGenerated by [Generative Pangea](https://www.generativepangea.com) - AI-powered documentation solutions`;

        // Store filename and corresponding content
        chapterFiles.push({ filename, content: chapterContent });
      } else {
        emitGraphStatus("CombineTutorial", 45, `Warning: Mismatch at index ${i} (abstraction index ${abstractionIndex})`);
        console.log(
          `Warning: Mismatch between chapter order, abstractions, or content at index ${i} (abstraction index ${abstractionIndex}). Skipping file generation for this entry.`
        );
      }
    }

    // Add attribution to index content (using English fixed string)
    indexContent += `---\n\nGenerated by [Generative Pangea](https://www.generativepangea.com) - AI-powered documentation solutions`;

    emitGraphStatus("CombineTutorial", 50, "Preparation complete, ready to write files");

    return {
      outputPath,
      indexContent,
      chapterFiles, // Array of {filename: string, content: string}
      projectName,
      project_description: relationshipsData.summary,
      repoUrl,
      language: shared.language || "english",
      user_id: shared.user_id,
      session_id: shared.session_id,
      tutorial_id: shared.tutorial_id
    };
  }

  async exec(prepRes: any): Promise<string> {
    const {  indexContent, chapterFiles, projectName, project_description, repoUrl, language,user_id , tutorial_id } = prepRes;
  
      return this.saveToSupabase(tutorial_id, indexContent, chapterFiles, projectName,project_description, repoUrl, language,user_id);
    
  }

  private async saveToSupabase( tutorialId: string, indexContent: string, chapterFiles: any[], projectName: string, project_description: string, repoUrl: string, language: string, user_id: string): Promise<string> {
   
   
    emitGraphStatus("CombineTutorial", 60, `Preparing to save tutorial to Supabase`);
    //console.log(`Saving tutorial to Supabase: ${projectName}`);

    if (!supabase) {
      throw new Error("Supabase client not initialized. Check your environment variables.");
    }

    try {
 

     
     

      // Save the index file to Supabase Storage
      emitGraphStatus("CombineTutorial", 70, "Saving index.md file");
      const indexPath = `${tutorialId}/index.md`;
      const indexBlob = new Blob([indexContent], { type: 'text/markdown' });

      const { data: indexData, error: indexError } = await supabase.storage
        .from(BUCKET_NAME)
        .upload(indexPath, indexBlob, {
          contentType: 'text/markdown',
          upsert: true
        });

      if (indexError) {
        console.error("Error uploading index.md:", indexError);
        // Show toast notification about the error
        toast({
          title: "Error Saving Tutorial",
          description: `Failed to upload index file: ${indexError.message}`,
          variant: "destructive"
        });
        throw indexError;
      }

      console.log(`Saved index.md to Supabase: ${indexPath}`);

      // Get public URL for the index file
      const { data: indexUrlData } = supabase.storage
        .from('tutorials')
        .getPublicUrl(indexPath);

      const indexUrl = indexUrlData.publicUrl;

      // Save chapter files
      emitGraphStatus("CombineTutorial", 80, `Saving ${chapterFiles.length} chapter files`);
      const chapterUrls = [];

      // Loop through chapter files
      for (const chapterInfo of chapterFiles) {
        const chapterPath = `${tutorialId}/${chapterInfo.filename}`;
        const chapterBlob = new Blob([chapterInfo.content], { type: 'text/markdown' });

        const { data: chapterData, error: chapterError } = await supabase.storage
          .from('tutorials')
          .upload(chapterPath, chapterBlob, {
            contentType: 'text/markdown',
            upsert: true
          });

        if (chapterError) {
          console.error(`Error uploading chapter ${chapterInfo.filename}:`, chapterError);
          // Show toast notification for chapter upload error
          toast({
            title: "Error Saving Chapter",
            description: `Failed to upload ${chapterInfo.filename}: ${chapterError.message}`,
            variant: "destructive"
          });
          throw chapterError;
        }

        console.log(`Saved chapter to Supabase: ${chapterPath}`);

        // Get public URL for the chapter
        const { data: chapterUrlData } = supabase.storage
          .from('tutorials')
          .getPublicUrl(chapterPath);

        chapterUrls.push({
          filename: chapterInfo.filename,
          url: chapterUrlData.publicUrl
        });
      }
      // End loop
      // Save tutorial metadata to Supabase database
      let  tutorialDBId;
      try {
        console.log("Saving tutorial metadata to database with:", {
          tutorial_id: tutorialId,
          project_name: projectName,
          index_url: indexUrl,
          chapter_urls: chapterUrls
        });

        const prompt = buildPrompt(PROJECT_SUMMARY_PROMPT, {
          project_description
        });

        const llm_project_summary= await callLlm_openrouter ( { tutorial_id: tutorialId, prompt, use_cache: true, temperature: 0.7, model: "google/gemma-3n-e4b-it:free", user_id }); // free model

        emitGraphStatus("CombineTutorial", 85, "Generating project summary");
        
        const { data: metaData, error: metaError } = await supabase
          .from('tutorial_metadata')
          .insert({
            tutorial_id: tutorialId,
            project_name: projectName,
            index_url: indexUrl,
            chapter_urls: chapterUrls,
            description: llm_project_summary,
            repo_url: repoUrl,
            language: language,
            user_id: user_id || null
           
          })
          .select();

        if (metaError) {
          console.error("Error saving to tutorial_metadata:", metaError);
          // Show toast notification
          toast({
            title: "Error Saving Metadata",
            description: `Failed to save tutorial metadata: ${metaError.message}`,
            variant: "destructive"
          });
          throw metaError;
        }
        tutorialDBId = metaData[0].id;

        // Generate cover image
        emitGraphStatus("CombineTutorial", 90, "Generating tutorial cover image");
        let hasCoverError = false;
        try {
          const { data: coverData, error: coverError } = await supabase.functions.invoke(
            'generate-tutorial-cover',
            {
              body: {
                tutorialId: tutorialId,
                projectName: projectName,
                description: llm_project_summary,
                language: language
              }
            }
          );

          if (coverError) {
            console.error("Error generating cover image:", coverError);
            hasCoverError = true;
            toast({
              title: "Cover Generation Warning",
              description: "Tutorial saved successfully but cover image generation failed",
              variant: "default"
            });
          } else {
            console.log("Cover image generated successfully:", coverData);
          }
        } catch (coverErr) {
          console.error("Error in cover generation:", coverErr);
          hasCoverError = true;
        }

        // Success toast notification (only if cover generation succeeded)
        if (!hasCoverError) {
          toast({
            title: "Tutorial Complete",
            description: "Tutorial and cover image generated successfully",
          });
        } else {
          toast({
            title: "Tutorial Saved",
            description: "Tutorial files and metadata saved successfully",
          });
        }
      } catch (metaErr) {
        console.error("Error in metadata saving:", metaErr);
        // If metadata fails but files saved, we should still return the ID
        toast({
          title: "Partial Success",
          description: "Tutorial files saved but metadata failed",
          variant: "destructive"
        });
      }

      emitGraphStatus("CombineTutorial", 95, "All files saved to Supabase successfully");
      return tutorialDBId; // Return the tutorial ID for reference
    } catch (error) {
      console.error("Error saving to Supabase:", error);
      // Show final error toast
      toast({
        title: "Error Saving Tutorial",
        description: `An unexpected error occurred: ${error.message || error}`,
        variant: "destructive"
      });
      throw error;
    }
  }

  async post(
    shared: SharedStore,
    _: any,
    execRes: string
  ): Promise<string | undefined> {
    if (isNode) {
      // Node.js environment
      emitGraphStatus("CombineTutorial", 95, `Storing output path in shared store: ${execRes}`);
      shared.final_output_dir = execRes; // Store the output path

      // Emit progress event
      emitProgress("Tutorial Compilation", 100, `Tutorial generated successfully in: ${execRes}`);

      // Final graph status
      emitGraphStatus("CombineTutorial", 100, "Tutorial compilation complete");

      // Emit complete event to signal the end of the flow
      emitComplete({ success: true, message: "Tutorial successfully generated" });
    } else {
      // Browser environment
      emitGraphStatus("CombineTutorial", 95, `Tutorial saved to Supabase with ID: ${execRes}`);
      shared.final_output_dir = execRes; // Store the tutorial ID

      if (supabase) {
        // Get the public URL for the index file
        const indexPath = `${execRes}/index.md`;
        const { data: urlData } = supabase.storage
          .from('tutorials')
          .getPublicUrl(indexPath);

        // Emit progress event
        emitProgress("Tutorial Compilation", 100, `Tutorial generated successfully. Access at: ${urlData.publicUrl}`);

        // Final graph status
        emitGraphStatus("CombineTutorial", 100, "Tutorial compilation complete");

        // Emit complete event with tutorial ID for browser environment
        emitComplete({ 
          success: true, 
          message: "Tutorial successfully generated",
          tutorialId: execRes // Include the tutorial ID in the complete event
        });
      } else {
        emitGraphStatus("CombineTutorial", 100, "Tutorial compilation complete");
        emitProgress("Tutorial Compilation", 100, "Tutorial generated successfully");
      }
    }

    return "default";
  }
}
