import { create_tutorial_flow } from '../src/Agents/Code2Documentation/flow/flow';
import { Abstraction, SharedStore } from '../src/Agents/Code2Documentation/types';

// Tutorial Flow Tests
async function testCreateTutorialFlow() {
  console.log('Testing tutorial flow creation...');
  
  // Create the flow
  const flow = create_tutorial_flow();
  
  // Check that the flow exists
  console.log('Flow created:', !!flow);
  
  // Create a test shared store with sample data
  const shared: SharedStore = {
   // repo_url: 'https://github.com/nikitavoloboev/ts',
   repo_url: 'https://github.com/EarthShaping/testautocode',
    //local_dir: 'C:/DEV_AI/GenerativePangea/repo-tutorial-forge/src/pocketflow',
    project_name: 'tssdsd:asa sa skas aska', // Can be None, FetchRepo will derive it
    github_token: "",
    output_dir: './output1', //Base directory for CombineTutorial output

    //Selected files to process
    selected_files: ['src/index.ts', 'src/main.ts', 'package.json', 'README.md'],

    //Add language for multi-language support
    language: 'english',

    //Add use_cache flag (inverse of no-cache flag)
    use_cache: true,

    //Add max_abstraction_num parameter
    max_abstraction_num: 10, // default is 10


    final_output_dir: './output',
  
  //    files: [] as [string, string][],
  //   abstractions: [] as Abstraction[] ,
  
  // relationships= { summary: '', details: [] },
  // //       "chapter_order": [],
  // //       "chapters": [],
  // //       "final_output_dir": None

  };
  
  
  // Test that the flow can be run without errors
  try {
    await flow.run(shared);
    console.log('Flow ran successfully');
  } catch (error) {
    console.error('Error running flow:', error);
  }
}


// Run the tests
async function runTests() {
  await testCreateTutorialFlow();

}

runTests().catch(console.error);

