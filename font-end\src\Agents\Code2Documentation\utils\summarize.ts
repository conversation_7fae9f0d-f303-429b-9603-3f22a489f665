
import { callLlm_openrouter } from '@/Agents/shared/callLlm_openrouter';

export async function summarize(text: string, user_id: string = 'anonymous', tutorial_id: string) {
  if (!text || text.trim().length === 0) {
    return '';
  }

  // If text is short enough, return as-is
  if (text.length <= 500) {
    return text;
  }

  const prompt = `Please provide a concise summary of the following text in 2-3 sentences. Focus on the main points and key information:

${text}

Summary:`;

  try {
    const summary = await callLlm_openrouter({
      prompt,
      use_cache: true,
      temperature: 0.1,
      user_id,
      tutorial_id
    });
    
    return summary.trim();
  } catch (error) {
    console.error('Error generating summary:', error);
    // Fallback: return first 500 characters with ellipsis
    return text.substring(0, 500) + '...';
  }
}

export async function summarizeChapter(chapterContent: string, chapterTitle: string, user_id: string = 'anonymous', tutorial_id: string) {
  if (!chapterContent || chapterContent.trim().length === 0) {
    return '';
  }

  const prompt = `Please provide a brief summary of this tutorial chapter titled "${chapterTitle}". Focus on what the reader will learn and the main concepts covered:

${chapterContent}

Summary (2-3 sentences):`;

  try {
    const summary = await callLlm_openrouter({
      prompt,
      use_cache: true,
      temperature: 0.1,
      user_id,
      tutorial_id
    });
    
    return summary.trim();
  } catch (error) {
    console.error('Error generating chapter summary:', error);
    // Fallback: return first 200 characters with ellipsis
    return chapterContent.substring(0, 200) + '...';
  }
}
