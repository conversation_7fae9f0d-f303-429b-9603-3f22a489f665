import React, { useState, useEffect, useRef } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Progress } from "@/components/ui/progress";
import { executeCode2TutorFlow, createDefaultSharedStore } from "@/Agents/Code2Tutor";
import { SharedStore } from "@/Agents/Code2Tutor/types";
import {
  tutorEvents,
  TutorEventType,
  ProgressEvent,
  AgentStatusEvent,
} from "@/Agents/Code2Tutor/utils/events";
import { CheckCircle, Clock, AlertCircle, Loader2, BookOpen, Target, Zap, Users } from "lucide-react";
import { toast } from "@/hooks/use-toast";



interface LogEntry {
  timestamp: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
}

interface TutorialStage {
  id: string;
  name: string;
  status: "pending" | "in-progress" | "completed" | "error";
  description: string;
  completedIn: string;
  details: string[];
  concepts?: number;
  exercises?: number;
}

interface AgentStatus {
  currentAgent: string;
  agentProgress: number;
  statusMessage: string;
}

const TutorCreationStatus = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const params = location.state;

  const [progress, setProgress] = useState(0);
  const [logEntries, setLogEntries] = useState<LogEntry[]>([]);
  const [agentStatus, setAgentStatus] = useState<AgentStatus>({
    currentAgent: "",
    agentProgress: 0,
    statusMessage: "Initializing...",
  });

  const logContainerRef = useRef<HTMLDivElement>(null);

  const [tutorialStages, setTutorialStages] = useState<TutorialStage[]>([
    {
      id: "repository-analysis",
      name: "Repository Analysis",
      status: "pending",
      description: "Analyzing repository structure and extracting educational content",
      completedIn: "",
      details: [],
    },
    {
      id: "concept-extraction",
      name: "Learning Concept Extraction",
      status: "pending",
      description: "Identifying key learning concepts and their relationships",
      completedIn: "",
      details: [],
      concepts: 0,
    },
    {
      id: "tutorial-planning",
      name: "Tutorial Structure Planning",
      status: "pending",
      description: "Designing optimal learning progression and dependencies",
      completedIn: "",
      details: [],
    },
    {
      id: "content-generation",
      name: "Interactive Content Generation",
      status: "pending",
      description: "Creating educational content, exercises, and examples",
      completedIn: "",
      details: [],
      exercises: 0,
    },
    {
      id: "tutorial-assembly",
      name: "Tutorial Assembly",
      status: "pending",
      description: "Combining all components into final interactive tutorial",
      completedIn: "",
      details: [],
    },
  ]);

  const flowInitialized = useRef(false);

  useEffect(() => {
    if (!session) {
      return;
    }

    if (!flowInitialized.current) {
      const repoUrl = params?.repoUrl || "https://github.com/example/demo-repo";
      handleGenerateTutorial(repoUrl, params);
      flowInitialized.current = true;
    }
  }, [params, session]);

  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logEntries]);

  const getTimestamp = (): string => {
    return new Date().toLocaleTimeString();
  };

  const addLogEntry = (message: string, type: LogEntry["type"] = "info") => {
    setLogEntries((prev) => [
      ...prev,
      {
        timestamp: getTimestamp(),
        message,
        type,
      },
    ]);
  };

  const updateStageStatus = (
    stageId: string,
    status: TutorialStage["status"],
    details?: string[],
    additionalData?: any
  ) => {
    setTutorialStages((prev) =>
      prev.map((stage) => {
        if (stage.id === stageId) {
          const updatedStage = {
            ...stage,
            status,
            ...(details && { details }),
            ...(status === "completed" && { completedIn: getTimestamp() }),
            ...additionalData,
          };
          return updatedStage;
        }
        return stage;
      })
    );
  };

  const handleGenerateTutorial = async (repoUrl: string, options: any) => {
    try {
      addLogEntry("🚀 Starting Code2Tutor workflow...", "info");

      // Map agent names to tutorial stages
      const agentToStageMap: Record<string, string> = {
        "RepoAnalysisAgent": "repository-analysis",
        "ConceptExtractionAgent": "concept-extraction",
        "TutorialPlanningAgent": "tutorial-planning",
        "ContentGenerationAgent": "content-generation",
        "TutorialAssemblyAgent": "tutorial-assembly",
      };

      // Set up event listeners
      const progressListener = tutorEvents.on(TutorEventType.PROGRESS, (data: ProgressEvent) => {
        setProgress(data.progress);
        addLogEntry(`📈 Progress: ${data.progress}% - ${data.message}`, "info");
      });

      const agentStatusListener = tutorEvents.on(TutorEventType.AGENT_STATUS, (data: AgentStatusEvent) => {
        setAgentStatus({
          currentAgent: data.agentName,
          agentProgress: data.progress,
          statusMessage: data.message || "",
        });

        const stageId = agentToStageMap[data.agentName];
        if (stageId) {
          if (data.status === "starting") {
            updateStageStatus(stageId, "in-progress");
            addLogEntry(`🤖 ${data.agentName}: Starting`, "info");
          } else if (data.status === "completed") {
            updateStageStatus(stageId, "completed", [data.message || "Completed successfully"]);
            addLogEntry(`✅ ${data.agentName}: Completed`, "success");
          } else if (data.status === "error") {
            updateStageStatus(stageId, "error", [data.message || "An error occurred"]);
            addLogEntry(`❌ ${data.agentName}: Error - ${data.message}`, "error");
          }
        }

        addLogEntry(`🔄 ${data.agentName}: ${data.message}`, "info");
      });

      const conceptListener = tutorEvents.on(TutorEventType.CONCEPT_EXTRACTED, (event) => {
        addLogEntry(`💡 Concept extracted: ${event.concept.name} (${event.concept.difficulty})`, "success");
        updateStageStatus("concept-extraction", "in-progress", undefined, {
          concepts: (tutorialStages.find(s => s.id === "concept-extraction")?.concepts || 0) + 1
        });
      });

      const sectionListener = tutorEvents.on(TutorEventType.SECTION_GENERATED, (event) => {
        addLogEntry(`📝 Section generated: ${event.section.title}`, "success");
        updateStageStatus("content-generation", "in-progress", undefined, {
          exercises: (tutorialStages.find(s => s.id === "content-generation")?.exercises || 0) + event.section.exercises.length
        });
      });

      const completeListener = tutorEvents.on(TutorEventType.COMPLETE, (event) => {
        if (event.success) {
          addLogEntry(`🎉 Tutorial created successfully!`, "success");
          addLogEntry(`🔗 Tutorial URL: ${event.tutorialUrl}`, "info");
          setProgress(100);
          
          toast({
            title: "Tutorial Created!",
            description: "Your interactive tutorial has been generated successfully.",
          });

          // Navigate to tutorial after a delay
          setTimeout(() => {
            if (event.tutorialId) {
              navigate(`/tutorial/${event.tutorialId}`);
            }
          }, 2000);
        } else {
          addLogEntry(`💥 Tutorial generation failed: ${event.message}`, "error");
          toast({
            title: "Generation Failed",
            description: event.message,
            variant: "destructive",
          });
        }
      });

      const errorListener = tutorEvents.on(TutorEventType.ERROR, (event) => {
        addLogEntry(`💥 Error: ${event.error.message}`, "error");
        toast({
          title: "Error",
          description: event.error.message,
          variant: "destructive",
        });
      });

      // Create shared store for Code2Tutor
      const shared: SharedStore = createDefaultSharedStore({
        user_id: session?.user?.id || 'anonymous',
        repo_url: repoUrl,
        project_name: options?.projectName,
        github_token: options?.githubToken || "",
        selected_files: options?.selectedFiles || [],
        target_audience: options?.targetAudience || "beginner",
        content_language: options?.contentLanguage || "english",
        tutorial_format: options?.tutorialFormat || "guided",
        max_concepts: options?.maxConcepts || 8,
        include_exercises: options?.includeExercises !== false,
        include_diagrams: options?.includeDiagrams !== false,
        include_examples: options?.includeExamples !== false,
        use_cache: options?.advancedOptions?.cacheDuration !== 0,
      });

      addLogEntry("🔧 Configuration prepared, starting workflow...", "info");

      // Execute the Code2Tutor workflow
      const results = await executeCode2TutorFlow(shared);

      addLogEntry(`📊 Workflow completed with ${results.length} steps`, "success");

      // Clean up event listeners
      progressListener();
      agentStatusListener();
      conceptListener();
      sectionListener();
      completeListener();
      errorListener();

    } catch (error) {
      console.error("Error running Code2Tutor workflow:", error);
      addLogEntry(`💥 Workflow error: ${error.message}`, "error");
      
      toast({
        title: "Workflow Error",
        description: "An unexpected error occurred during tutorial generation.",
        variant: "destructive",
      });
    }
  };

  const getStageIcon = (status: TutorialStage["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "in-progress":
        return <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />;
      case "error":
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStageColor = (status: TutorialStage["status"]) => {
    switch (status) {
      case "completed":
        return "border-green-200 bg-green-50";
      case "in-progress":
        return "border-blue-200 bg-blue-50";
      case "error":
        return "border-red-200 bg-red-50";
      default:
        return "border-gray-200 bg-gray-50";
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <BookOpen className="h-8 w-8 text-blue-600" />
          <h1 className="text-4xl font-bold text-gray-900">Creating Interactive Tutorial</h1>
        </div>
        <p className="text-xl text-gray-600">
          Transforming your code into an engaging learning experience with exercises and interactive content.
        </p>
        <Link
          to="/dashboard/create-tutor"
          className="text-blue-600 hover:text-blue-700 cursor-pointer"
        >
          <i className="fa-solid fa-arrow-left mr-2"></i>
          Back to configuration
        </Link>
      </div>

      {/* Overall Progress */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Overall Progress</h2>
          <span className="text-lg font-medium text-gray-700">{progress}%</span>
        </div>
        <Progress value={progress} className="h-3 mb-4" />
        
        {agentStatus.currentAgent && (
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-center space-x-3">
              <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
              <div>
                <div className="font-semibold text-blue-900">{agentStatus.currentAgent}</div>
                <div className="text-sm text-blue-700">{agentStatus.statusMessage}</div>
                <div className="w-full bg-blue-200 h-2 mt-2 rounded-full overflow-hidden">
                  <div
                    className="bg-blue-600 h-full rounded-full transition-all duration-300"
                    style={{ width: `${agentStatus.agentProgress}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Tutorial Stages */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900">Tutorial Creation Stages</h2>
          
          <div className="space-y-4">
            {tutorialStages.map((stage, index) => (
              <div
                key={stage.id}
                className={`p-4 rounded-lg border ${getStageColor(stage.status)}`}
              >
                <div className="flex items-start space-x-3">
                  {getStageIcon(stage.status)}
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900">{stage.name}</h3>
                      {stage.completedIn && (
                        <span className="text-xs text-gray-500">
                          Completed at {stage.completedIn}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{stage.description}</p>
                    
                    {/* Stage-specific metrics */}
                    {stage.concepts !== undefined && stage.concepts > 0 && (
                      <div className="flex items-center space-x-2 mt-2">
                        <Target className="h-4 w-4 text-blue-600" />
                        <span className="text-sm text-blue-700">{stage.concepts} concepts identified</span>
                      </div>
                    )}
                    
                    {stage.exercises !== undefined && stage.exercises > 0 && (
                      <div className="flex items-center space-x-2 mt-2">
                        <Zap className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-green-700">{stage.exercises} exercises created</span>
                      </div>
                    )}

                    {stage.details.length > 0 && (
                      <div className="mt-2 space-y-1">
                        {stage.details.map((detail, idx) => (
                          <div key={idx} className="text-xs text-gray-600">
                            • {detail}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Activity Log */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900">Activity Log</h2>
          
          <div
            ref={logContainerRef}
            className="bg-gray-900 text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm"
          >
            {logEntries.map((entry, index) => (
              <div key={index} className="mb-1">
                <span className="text-gray-500">[{entry.timestamp}]</span>{" "}
                <span
                  className={
                    entry.type === "error"
                      ? "text-red-400"
                      : entry.type === "success"
                      ? "text-green-400"
                      : entry.type === "warning"
                      ? "text-yellow-400"
                      : "text-blue-400"
                  }
                >
                  {entry.message}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Status Toast */}
      {progress < 100 && agentStatus.currentAgent && (
        <div className="fixed bottom-4 right-4 bg-blue-600 text-white px-6 py-3 rounded-md flex items-center shadow-lg">
          <Loader2 className="h-5 w-5 text-white mr-3 animate-spin" />
          <div>
            <div className="font-semibold">{agentStatus.currentAgent}</div>
            <div className="text-sm">{agentStatus.statusMessage}</div>
            <div className="w-full bg-white/20 h-1 mt-1 rounded-full overflow-hidden">
              <div
                className="bg-white h-full rounded-full transition-all duration-300"
                style={{ width: `${agentStatus.agentProgress}%` }}
              ></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TutorCreationStatus;
