
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import JoinWaitingListModal from "./JoinWaitingListModal";

const WaitingList = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <div className="bg-gray-50 py-20">
      <div className="container mx-auto px-6 text-center">
        <div className="max-w-3xl mx-auto animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Be the First to Experience the Future of
            <span className="text-gray-700"> AI-Powered Code Learning</span>
          </h2>

          <p className="text-xl text-gray-600 mb-8">
            Join our exclusive <strong>waiting list</strong> and get <strong>free early access</strong> to CodeTutorPro before our official launch.
            Help shape the future of <strong>AI-powered programming tutorials</strong> and <strong>code education</strong>.
          </p>

          <div className="flex justify-center mb-8">
            <Button
              onClick={() => setIsModalOpen(true)}
              className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-3 text-lg font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              Get Early Access
            </Button>
          </div>

          <div className="flex flex-col sm:flex-row gap-8 justify-center items-center text-sm text-gray-500">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
              <span>Free early access</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
              <span>No spam, unsubscribe anytime</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
              <span>Exclusive updates</span>
            </div>
          </div>
        </div>
      </div>

      <JoinWaitingListModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
};

export default WaitingList;
