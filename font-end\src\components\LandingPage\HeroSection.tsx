
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";

export function HeroSection() {
  return (
    <section className="bg-gradient-to-br from-tutorial-primary via-tutorial-secondary to-tutorial-accent py-20 text-white relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 bg-black/5"></div>
      <div className="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-20 right-10 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Content */}
          <div className="text-center lg:text-left">
            <div className="inline-flex items-center bg-white/20 rounded-full px-4 py-2 mb-6 backdrop-blur-sm">
              <i className="fa-solid fa-sparkles text-yellow-300 mr-2"></i>
              <span className="text-sm font-medium">AI-Powered Documentation</span>
            </div>
            
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
              Turn Any GitHub Repo into
              <span className="block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                Step-by-Step Tutorials
              </span>
            </h1>
            
            <p className="text-lg md:text-xl mb-8 opacity-95 leading-relaxed max-w-2xl mx-auto lg:mx-0">
              Stop spending weeks writing documentation. Our AI analyzes your code and creates 
              comprehensive, beginner-friendly tutorials in minutes. Perfect for onboarding, 
              documentation, and knowledge sharing.
            </p>
            
            {/* Value props */}
            <div className="flex flex-wrap gap-6 mb-8 justify-center lg:justify-start">
              <div className="flex items-center text-sm">
                <i className="fa-solid fa-clock text-green-300 mr-2"></i>
                Save 90% documentation time
              </div>
              <div className="flex items-center text-sm">
                <i className="fa-solid fa-users text-blue-300 mr-2"></i>
                Onboard developers 10x faster
              </div>
              <div className="flex items-center text-sm">
                <i className="fa-solid fa-rocket text-purple-300 mr-2"></i>
                Generate in under 5 minutes
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
              <Link to="/dashboard">
                <Button
                  size="lg"
                  className="bg-white text-tutorial-primary hover:bg-gray-100 text-lg px-8 py-4 shadow-xl font-semibold"
                >
                  Start Free Trial
                  <i className="fa-solid fa-arrow-right ml-2"></i>
                </Button>
              </Link>
              <Link to="/public-gallery">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-2 border-white text-white hover:bg-white/10 text-lg px-8 py-4 backdrop-blur-sm font-semibold"
                >
                  <i className="fa-solid fa-play mr-2"></i>
                  See Examples
                </Button>
              </Link>
            </div>
            
            <div className="flex justify-center lg:justify-start items-center space-x-8 text-sm opacity-80">
              <div className="flex items-center">
                <i className="fa-solid fa-check-circle text-green-300 mr-2"></i>
                7-day free trial
              </div>
              <div className="flex items-center">
                <i className="fa-solid fa-credit-card text-red-300 mr-2 line-through"></i>
                No credit card required
              </div>
              <div className="flex items-center">
                <i className="fa-solid fa-infinity text-blue-300 mr-2"></i>
                Cancel anytime
              </div>
            </div>
          </div>
          
          {/* Right side - Demo preview */}
          <div className="lg:block hidden">
            <div className="relative">
              <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 shadow-2xl border border-white/20">
                <div className="bg-gray-900 rounded-lg p-4 mb-4">
                  <div className="flex items-center mb-3">
                    <div className="flex space-x-2">
                      <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    </div>
                    <div className="ml-4 text-gray-400 text-sm">github.com/your-repo</div>
                  </div>
                  <div className="text-green-400 text-sm font-mono">
                    <div>$ git clone https://github.com/user/repo.git</div>
                    <div className="text-gray-500 mt-1">Analyzing repository...</div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between bg-white/5 rounded-lg p-3">
                    <div className="flex items-center">
                      <i className="fa-solid fa-magic-wand-sparkles text-purple-300 mr-3"></i>
                      <span className="text-sm">AI Analysis Complete</span>
                    </div>
                    <i className="fa-solid fa-check text-green-300"></i>
                  </div>
                  
                  <div className="flex items-center justify-between bg-white/5 rounded-lg p-3">
                    <div className="flex items-center">
                      <i className="fa-solid fa-book text-blue-300 mr-3"></i>
                      <span className="text-sm">Tutorial Generated</span>
                    </div>
                    <i className="fa-solid fa-check text-green-300"></i>
                  </div>
                  
                  <div className="bg-gradient-to-r from-green-500 to-blue-500 rounded-lg p-4 text-center">
                    <i className="fa-solid fa-graduation-cap text-2xl mb-2"></i>
                    <div className="text-sm font-semibold">Ready to Share!</div>
                  </div>
                </div>
              </div>
              
              {/* Floating elements */}
              <div className="absolute -top-4 -right-4 bg-yellow-400 text-gray-900 rounded-full w-16 h-16 flex items-center justify-center font-bold shadow-lg animate-bounce">
                <i className="fa-solid fa-bolt"></i>
              </div>
              <div className="absolute -bottom-4 -left-4 bg-green-400 text-gray-900 rounded-full w-12 h-12 flex items-center justify-center shadow-lg animate-pulse">
                <i className="fa-solid fa-check"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
