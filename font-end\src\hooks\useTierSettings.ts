
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { toast } from './use-toast';

type TierSetting = {
  id: string;
  tier_name: string;
  max_tutorials_per_month: number;
  max_file_size_mb: number;
  can_access_private_repos: boolean;
  has_priority_support: boolean;
  has_api_access: boolean;
  has_custom_branding: boolean;
  has_trial: boolean;
  trial_days: number;
  price_monthly: number;
  trial_tutorials_limit: number;
  features: string[];
  created_at: string;
  updated_at: string;
};

export const useTierSettings = () => {
  const { user, isAdmin } = useAuth();
  const [tierSettings, setTierSettings] = useState<TierSetting[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchTierSettings();
  }, []);

  const fetchTierSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('tier_settings')
        .select('*')
        .order('price_monthly');

      if (error) throw error;

      setTierSettings(data || []);
    } catch (error) {
      console.error('Error fetching tier settings:', error);
      toast({
        title: "Error",
        description: "Failed to load tier settings",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateTierSetting = async (id: string, updates: Partial<TierSetting>) => {
    if (!isAdmin) {
      toast({
        title: "Access Denied",
        description: "Only admins can modify tier settings",
        variant: "destructive",
      });
      return;
    }

    setSaving(true);
    try {
      const { error } = await supabase
        .from('tier_settings')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) throw error;

      // Update local state
      setTierSettings(prev => 
        prev.map(tier => 
          tier.id === id 
            ? { ...tier, ...updates, updated_at: new Date().toISOString() }
            : tier
        )
      );

      toast({
        title: "Success",
        description: "Tier settings updated successfully",
      });
    } catch (error) {
      console.error('Error updating tier settings:', error);
      toast({
        title: "Error",
        description: "Failed to update tier settings",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  return {
    tierSettings,
    loading,
    saving,
    updateTierSetting,
    refreshTierSettings: fetchTierSettings
  };
};
