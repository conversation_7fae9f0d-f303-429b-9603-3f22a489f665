
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";

export function HowItWorksSection() {
  const steps = [
    {
      number: "01",
      icon: "fa-solid fa-link",
      title: "Paste GitHub URL",
      description: "Simply paste any GitHub repository URL. Works with public and private repos.",
      color: "from-blue-500 to-blue-600"
    },
    {
      number: "02", 
      icon: "fa-solid fa-brain",
      title: "AI Analyzes Code",
      description: "Our AI reads your code, understands patterns, and identifies key concepts to teach.",
      color: "from-purple-500 to-purple-600"
    },
    {
      number: "03",
      icon: "fa-solid fa-book-open",
      title: "Tutorial Generated",
      description: "Get a comprehensive tutorial with explanations, code examples, and step-by-step guides.",
      color: "from-green-500 to-green-600"
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-tutorial-primary/10 rounded-full px-4 py-2 mb-4">
            <span className="text-tutorial-primary font-semibold text-sm">HOW IT WORKS</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-800">
            From Code to Tutorial in
            <span className="text-tutorial-primary"> 3 Simple Steps</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our AI-powered platform makes creating comprehensive documentation effortless.
            No more spending weeks writing guides - get professional tutorials in minutes.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 relative">
          {/* Connection lines for desktop */}
          <div className="hidden md:block absolute top-1/2 left-1/3 right-1/3 h-0.5 bg-gradient-to-r from-tutorial-primary to-tutorial-secondary transform -translate-y-1/2 z-0"></div>
          
          {steps.map((step, index) => (
            <Card key={index} className="border-0 shadow-xl hover:shadow-2xl transition-all duration-300 relative z-10 group hover:-translate-y-2">
              <CardHeader className="text-center pb-4">
                <div className="relative mx-auto mb-6">
                  <div className={`w-20 h-20 bg-gradient-to-r ${step.color} rounded-full flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <i className={`${step.icon} text-white text-3xl`}></i>
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-gray-100">
                    <span className="text-xs font-bold text-gray-600">{step.number}</span>
                  </div>
                </div>
                <CardTitle className="text-2xl mb-3 text-gray-800">
                  {step.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base leading-relaxed text-gray-600 text-center">
                  {step.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="text-center mt-16">
          <div className="inline-flex items-center bg-green-50 border border-green-200 rounded-full px-6 py-3">
            <i className="fa-solid fa-clock text-green-600 mr-2"></i>
            <span className="text-green-800 font-semibold">Average generation time: Under 5 minutes</span>
          </div>
        </div>
      </div>
    </section>
  );
}
