# Technical Context: CodeTutorPro

## Technology Stack

### Frontend Technologies
- **React 18.3.1**: Modern React with hooks and concurrent features
- **TypeScript 5.5.3**: Type safety and enhanced developer experience
- **Vite 5.4.1**: Fast build tool and development server
- **React Router 6.26.2**: Client-side routing with protected routes
- **Tailwind CSS 3.4.11**: Utility-first CSS framework
- **shadcn/ui**: High-quality, accessible component library

### State Management
- **Zustand 5.0.5**: Lightweight state management for user data
- **React Query (@tanstack/react-query 5.56.2)**: Server state management and caching
- **React Hook Form 7.53.0**: Form state management with validation

### Backend Services
- **Supabase 2.49.7**: 
  - PostgreSQL database
  - Authentication and authorization
  - Real-time subscriptions
  - File storage and CDN
- **Clerk**: User authentication and subscription management

### AI and Processing
- **PocketFlow**: Custom LLM workflow orchestration framework
- **OpenAI 4.100.0**: GPT models for content generation
- **OpenRouter**: Alternative LLM provider integration

### External Integrations
- **GitHub API (@octokit/rest 21.1.1)**: Repository access and file retrieval
- **Mermaid 11.6.0**: Diagram generation for tutorials
- **Canvas 3.1.0**: Image processing capabilities

### Development Tools
- **ESLint 9.9.0**: Code linting and style enforcement
- **TypeScript ESLint 8.0.1**: TypeScript-specific linting rules
- **Autoprefixer 10.4.20**: CSS vendor prefix automation
- **PostCSS 8.4.47**: CSS processing pipeline

## Development Environment

### Local Setup Requirements
- **Node.js**: Version 18+ (managed via nvm)
- **Package Manager**: npm, yarn, or pnpm
- **Environment Variables**: Supabase, Clerk, and API keys

### Build Configuration
- **Vite Config**: Optimized for React with SWC compiler
- **TypeScript Config**: Strict mode with path mapping
- **Tailwind Config**: Custom design tokens and component styles
- **ESLint Config**: React and TypeScript best practices

### Deployment
- **Platform**: Lovable (automated deployment)
- **Build Process**: Vite production build
- **Environment**: Serverless deployment with CDN

## Database Schema (Supabase)

### Core Tables
- **users**: User profiles and subscription data
- **tutorials**: Generated tutorial metadata
- **user_settings**: User preferences and configuration
- **usage_tracking**: Monthly tutorial generation tracking
- **admin_analytics**: System metrics and monitoring

### Authentication
- **Clerk Integration**: External authentication provider
- **Row Level Security (RLS)**: Database-level access control
- **JWT Validation**: Secure API access patterns

## API Architecture

### Frontend API Patterns
- **React Query**: Declarative data fetching with caching
- **Custom Hooks**: Encapsulated API logic (useAuth, useTutorials, etc.)
- **Error Boundaries**: Graceful error handling and recovery

### External API Integration
- **GitHub API**: Repository metadata and file content retrieval
- **OpenAI API**: LLM content generation
- **Supabase API**: Database operations and real-time updates

## File Structure Conventions

### Source Organization
```
src/
├── Agents/           # PocketFlow AI agents
├── components/       # React components
├── hooks/           # Custom React hooks
├── integrations/    # External service integrations
├── pages/           # Route components
├── stores/          # Zustand state stores
├── types/           # TypeScript type definitions
├── utils/           # Utility functions
└── constants/       # Application constants
```

### Component Organization
- **UI Components**: Reusable design system components
- **Feature Components**: Business logic components
- **Layout Components**: Page structure and navigation
- **Page Components**: Route-level components

## Performance Considerations

### Frontend Optimization
- **Code Splitting**: Route-based lazy loading
- **Bundle Analysis**: Vite bundle analyzer for optimization
- **Image Optimization**: Lazy loading and responsive images
- **Caching Strategy**: React Query with appropriate cache times

### Backend Optimization
- **Database Indexing**: Optimized queries for user and tutorial data
- **Connection Pooling**: Supabase connection management
- **Real-time Subscriptions**: Efficient WebSocket usage

## Security Implementation

### Authentication Security
- **JWT Tokens**: Secure token-based authentication
- **HTTPS Only**: Encrypted communication
- **CORS Configuration**: Restricted cross-origin requests

### Data Security
- **Environment Variables**: Secure API key management
- **Input Validation**: Client and server-side validation
- **SQL Injection Prevention**: Parameterized queries via Supabase

## Monitoring and Analytics

### Error Tracking
- **Console Logging**: Development debugging
- **Toast Notifications**: User-friendly error messages
- **Error Boundaries**: React error containment

### Performance Monitoring
- **Vite Dev Tools**: Development performance insights
- **React Query DevTools**: API call monitoring
- **Supabase Dashboard**: Database performance metrics
