// src/Agents/Code2Tutor/agents/RepoAnalysisAgent.ts

import { AgentComponent, ExecutionContext, ExecutionResult } from '../../../pangeaflow';
import { SharedStore } from '../types';
import { emitAgentStatus, emitTutorProgress } from '../utils/events';
import { fetch_selected_github_files } from '../../Code2Documentation/utils/crawl_github_files';
import { fetch_selected_local_files } from '../../Code2Documentation/utils/crawl_local_files';

/**
 * RepoAnalysisAgent - Analyzes repository structure and fetches relevant files
 * 
 * This agent is responsible for:
 * - Fetching files from GitHub repositories or local directories
 * - Filtering files based on include/exclude patterns
 * - Analyzing repository structure for educational content
 * - Preparing file context for concept extraction
 */
export class RepoAnalysisAgent extends AgentComponent {
  constructor(eventBus: any, telemetry: any) {
    super('repo-analysis', eventBus, telemetry, {
      stage: 'repository-analysis',
      progress: 0
    });
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('repo-analysis', async () => {
      emitAgentStatus('RepoAnalysisAgent', 'starting', 0, 'Initializing repository analysis');
      
      const shared = context.metadata.shared as SharedStore;
      
      try {
        // Validate required inputs
        if (!shared.repo_url && !shared.local_dir) {
          throw new Error('Either repo_url or local_dir must be provided');
        }

        emitAgentStatus('RepoAnalysisAgent', 'processing', 10, 'Fetching repository files');
        emitTutorProgress('Repository Analysis', 10, 'Starting file retrieval');

        let files: [string, string][] = [];

        if (shared.repo_url) {
          // Fetch from GitHub repository
          emitAgentStatus('RepoAnalysisAgent', 'processing', 20, 'Fetching files from GitHub repository');

          const result = await fetch_selected_github_files(
            shared.repo_url,
            shared.selected_files || [],
            {
              githubToken: shared.github_token,
              useRelativePaths: true
            }
          );
          files = Object.entries(result.files || {});
        } else if (shared.local_dir) {
          // Fetch from local directory
          emitAgentStatus('RepoAnalysisAgent', 'processing', 20, 'Fetching files from local directory');

          const result = await fetch_selected_local_files(
            shared.local_dir,
            shared.selected_files || [],
            {
              useRelativePaths: true
            }
          );
          files = Object.entries(result.files || {});
        }

        emitAgentStatus('RepoAnalysisAgent', 'processing', 60, `Retrieved ${files.length} files`);
        emitTutorProgress('Repository Analysis', 60, `Analyzed ${files.length} files`);

        // Filter files for educational relevance
        const educationalFiles = this.filterEducationalFiles(files);
        
        emitAgentStatus('RepoAnalysisAgent', 'processing', 80, `Filtered to ${educationalFiles.length} educational files`);

        // Analyze repository structure
        const repoStructure = this.analyzeRepoStructure(educationalFiles);
        
        emitAgentStatus('RepoAnalysisAgent', 'processing', 90, 'Analyzing repository structure');

        // Update shared store
        shared.files = educationalFiles;
        
        emitAgentStatus('RepoAnalysisAgent', 'completed', 100, 'Repository analysis completed');
        emitTutorProgress('Repository Analysis', 100, `Ready for concept extraction with ${educationalFiles.length} files`);

        this.emit('repo.analyzed', {
          fileCount: educationalFiles.length,
          structure: repoStructure,
          languages: this.detectLanguages(educationalFiles)
        }, context.id);

        return {
          success: true,
          data: {
            files: educationalFiles,
            structure: repoStructure,
            fileCount: educationalFiles.length
          },
          events: [],
          nextActions: ['extract-concepts'],
          metadata: {
            filesAnalyzed: files.length,
            educationalFiles: educationalFiles.length,
            primaryLanguage: this.detectPrimaryLanguage(educationalFiles)
          }
        };

      } catch (error) {
        emitAgentStatus('RepoAnalysisAgent', 'error', 0, `Error: ${error.message}`);
        
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['error'],
          metadata: { stage: 'repo-analysis' }
        };
      }
    });
  }

  /**
   * Filter files to focus on those most relevant for educational content
   */
  private filterEducationalFiles(files: [string, string][]): [string, string][] {
    const educationalPatterns = [
      /\.(js|jsx|ts|tsx|py|java|go|rs|cpp|c|h)$/i, // Source code files
      /\.(md|rst|txt)$/i, // Documentation files
      /^(README|CONTRIBUTING|CHANGELOG)/i, // Important docs
      /\.(json|yaml|yml)$/i, // Configuration files
      /Dockerfile$/i, // Docker files
      /Makefile$/i // Build files
    ];

    const excludePatterns = [
      /node_modules/,
      /\.git/,
      /dist/,
      /build/,
      /coverage/,
      /\.test\./,
      /\.spec\./,
      /test/,
      /tests/,
      /\.min\./,
      /\.bundle\./
    ];

    return files.filter(([path, content]) => {
      // Check if file should be excluded
      if (excludePatterns.some(pattern => pattern.test(path))) {
        return false;
      }

      // Check if file matches educational patterns
      if (educationalPatterns.some(pattern => pattern.test(path))) {
        return true;
      }

      // Include files with substantial content (likely to be educational)
      return content.length > 100 && content.length < 10000;
    });
  }

  /**
   * Analyze repository structure to understand project organization
   */
  private analyzeRepoStructure(files: [string, string][]): any {
    const structure = {
      directories: new Set<string>(),
      fileTypes: new Map<string, number>(),
      totalFiles: files.length,
      avgFileSize: 0
    };

    let totalSize = 0;

    files.forEach(([path, content]) => {
      // Extract directory
      const dir = path.substring(0, path.lastIndexOf('/'));
      if (dir) structure.directories.add(dir);

      // Extract file extension
      const ext = path.substring(path.lastIndexOf('.') + 1).toLowerCase();
      structure.fileTypes.set(ext, (structure.fileTypes.get(ext) || 0) + 1);

      totalSize += content.length;
    });

    structure.avgFileSize = Math.round(totalSize / files.length);

    return {
      directories: Array.from(structure.directories),
      fileTypes: Object.fromEntries(structure.fileTypes),
      totalFiles: structure.totalFiles,
      avgFileSize: structure.avgFileSize
    };
  }

  /**
   * Detect programming languages used in the repository
   */
  private detectLanguages(files: [string, string][]): string[] {
    const languageMap: Record<string, string> = {
      'js': 'JavaScript',
      'jsx': 'JavaScript',
      'ts': 'TypeScript',
      'tsx': 'TypeScript',
      'py': 'Python',
      'java': 'Java',
      'go': 'Go',
      'rs': 'Rust',
      'cpp': 'C++',
      'c': 'C',
      'h': 'C/C++',
      'php': 'PHP',
      'rb': 'Ruby',
      'cs': 'C#'
    };

    const languages = new Set<string>();
    
    files.forEach(([path]) => {
      const ext = path.substring(path.lastIndexOf('.') + 1).toLowerCase();
      if (languageMap[ext]) {
        languages.add(languageMap[ext]);
      }
    });

    return Array.from(languages);
  }

  /**
   * Detect the primary programming language
   */
  private detectPrimaryLanguage(files: [string, string][]): string {
    const languages = this.detectLanguages(files);
    return languages[0] || 'Unknown';
  }
}
