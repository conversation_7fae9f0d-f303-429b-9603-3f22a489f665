
import {
  Card,
  CardContent,
} from "@/components/ui/card";

export function TestimonialsSection() {
  const testimonials = [
    {
      rating: 5,
      text: "CodeTutor saved me weeks of documentation work. It generated comprehensive tutorials for our entire codebase in just a few hours. Our new developers are onboarding 10x faster now!",
      name: "<PERSON>",
      title: "Senior Developer at TechCorp",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=64&h=64&fit=crop&crop=face",
      metric: "10x faster onboarding"
    },
    {
      rating: 5,
      text: "Perfect for open source projects. Instead of spending months writing documentation, I just paste my repo URL and get professional tutorials. My GitHub stars increased 300%!",
      name: "<PERSON>",
      title: "Open Source Maintainer",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=64&h=64&fit=crop&crop=face",
      metric: "300% more GitHub stars"
    },
    {
      rating: 5,
      text: "Game changer for our team! We went from 2 weeks of documentation work to 5 minutes. The tutorials are so clear that even junior developers understand complex architecture immediately.",
      name: "<PERSON>",
      title: "CT<PERSON> at StartupXYZ",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=64&h=64&fit=crop&crop=face",
      metric: "2 weeks → 5 minutes"
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-yellow-50 border border-yellow-200 rounded-full px-4 py-2 mb-4">
            <i className="fa-solid fa-star text-yellow-500 mr-2"></i>
            <span className="text-yellow-800 font-semibold text-sm">TESTIMONIALS</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-800">
            Loved by <span className="text-tutorial-primary">15,000+</span> Developers
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            See how CodeTutor is transforming the way teams create documentation and onboard developers.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
              <CardContent className="p-8">
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <i key={i} className="fa-solid fa-star text-yellow-400 text-lg"></i>
                  ))}
                </div>
                
                <blockquote className="text-gray-700 mb-6 leading-relaxed text-lg">
                  "{testimonial.text}"
                </blockquote>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <img
                      src={testimonial.image}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full mr-4 border-2 border-gray-100"
                    />
                    <div>
                      <div className="font-semibold text-gray-800">{testimonial.name}</div>
                      <div className="text-sm text-gray-600">{testimonial.title}</div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="inline-flex items-center bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm font-semibold">
                    <i className="fa-solid fa-chart-line mr-2"></i>
                    {testimonial.metric}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <div className="inline-flex items-center space-x-8 text-gray-600">
            <div className="flex items-center">
              <i className="fa-solid fa-users text-tutorial-primary mr-2"></i>
              <span className="font-semibold">15,000+ developers</span>
            </div>
            <div className="flex items-center">
              <i className="fa-solid fa-building text-tutorial-primary mr-2"></i>
              <span className="font-semibold">500+ companies</span>
            </div>
            <div className="flex items-center">
              <i className="fa-solid fa-heart text-red-500 mr-2"></i>
              <span className="font-semibold">98% satisfaction</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
