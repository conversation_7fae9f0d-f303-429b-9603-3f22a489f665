import { ReactNode } from "react";
import { useAuth } from "@/hooks/useAuth";

import { Navigate, Outlet } from "react-router-dom";
//https://www.robinwieruch.de/react-router-private-routes/

interface ProtectedRouteProps {
  children?: ReactNode;
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const { isSignedIn, isLoaded } = useAuth();

  // Show loading state while auth is being determined
  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Show auth interface if not signed in
  if (!isSignedIn) {
    return <Navigate to="/auth" />;
  }

  return children ? children : <Outlet />;
};

export default ProtectedRoute;
