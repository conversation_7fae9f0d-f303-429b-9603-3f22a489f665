import { Outlet } from "react-router-dom";
import NavBar from "./NavBar";
import ChatBotWrapper from "../ChatBotWrapper";
import { FooterDashboard } from "./FooterDashboard";

import { useState, useEffect } from "react";
import { User, Session } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";
import { Header } from "./Header";
import { Footer } from "./Footer";

export function TutorialLayout() {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data } = await supabase.auth.getSession();
      setSession(data.session);
      setUser(data.session?.user ?? null);
      setLoading(false);
    };

    getInitialSession();

    // Set up auth state listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, []);
  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {session ? <NavBar /> : <Header />}
      <main className="w-full px-4 py-2 md:py-3 flex-1">
        <Outlet /> {/* Content specific to the route will be rendered here */}
      </main>

      {/* Footer */}
         {session ? <FooterDashboard /> : <Footer />}
     
      {/* Chat Bot */}
      <ChatBotWrapper />
    </div>
  );
}
