/**
 * PocketFlow - A minimalist framework for building LLM-powered workflows
 * 
 * This module provides the core abstractions for creating directed graphs of nodes
 * that can be used to build complex LLM applications like agents, RAG systems,
 * and multi-step workflows. The framework is designed to be lightweight (100 lines)
 * with zero dependencies while still being expressive enough for complex applications.
 * 
 * Core concepts:
 * - Node: A single unit of computation with prep, exec, and post lifecycle methods
 * - Flow: Orchestrates the execution of connected nodes
 * - Action: String labels for edges between nodes
 * - Shared Store: Data passed between nodes to maintain state
 */

/** 
 * Represents an object that cannot be iterated over.
 * Used to constrain parameter types to non-iterable objects.
 */
type NonIterableObject = Partial<Record<string, unknown>> & {
  [Symbol.iterator]?: never;
};

/** String label for edges between nodes in the graph */
type Action = string;

/**
 * Base class for all nodes in the workflow graph
 * 
 * Provides the core lifecycle methods (prep, exec, post) and connection mechanisms
 * for building directed graphs. Each node can have multiple successors linked by
 * named actions.
 * 
 * @template S Type of the shared state passed between nodes
 * @template P Type of parameters specific to this node
 */
class BaseNode<S = unknown, P extends NonIterableObject = NonIterableObject> {
  /** Parameters specific to this node instance */
  protected _params: P = {} as P;
  
  /** Map of action names to successor nodes */
  protected _successors: Map<Action, BaseNode> = new Map();
  
  /**
   * Internal execution method that calls the public exec method
   * Allows subclasses to add behavior around execution
   * 
   * @param prepRes Result from the prep phase
   * @returns Result of execution
   */
  protected async _exec(prepRes: unknown): Promise<unknown> {
    return await this.exec(prepRes);
  }
  
  /**
   * Preparation phase - runs before execution
   * Override this method to prepare data needed for execution
   * 
   * @param shared Shared state object passed between nodes
   * @returns Data to be passed to the exec phase
   */
  async prep(shared: S): Promise<unknown> {
    return undefined;
  }
  
  /**
   * Execution phase - main computation of the node
   * Override this method to implement the node's primary functionality
   * 
   * @param prepRes Result from the prep phase
   * @returns Result of execution to be passed to post phase
   */
  async exec(prepRes: unknown): Promise<unknown> {
    return undefined;
  }
  
  /**
   * Post-execution phase - runs after execution
   * Override this method to process results and determine next action
   * 
   * @param shared Shared state object passed between nodes
   * @param prepRes Result from the prep phase
   * @param execRes Result from the exec phase
   * @returns Action name to determine the next node, or undefined to end flow
   */
  async post(
    shared: S,
    prepRes: unknown,
    execRes: unknown
  ): Promise<Action | undefined> {
    return undefined;
  }
  
  /**
   * Internal method that runs the complete node lifecycle (prep -> exec -> post)
   * 
   * @param shared Shared state object passed between nodes
   * @returns Action name to determine the next node
   */
  async _run(shared: S): Promise<Action | undefined> {
    const p = await this.prep(shared),
      e = await this._exec(p);
    return await this.post(shared, p, e);
  }
  
  /**
   * Public method to run a single node (without successors)
   * Warns if successors exist since they won't be executed
   * 
   * @param shared Shared state object passed between nodes
   * @returns Action name returned from post phase
   */
  async run(shared: S): Promise<Action | undefined> {
    if (this._successors.size > 0)
      console.warn("Node won't run successors. Use Flow.");
    return await this._run(shared);
  }
  
  /**
   * Sets parameters for this node instance
   * 
   * @param params Parameters object to set
   * @returns This node instance for chaining
   */
  setParams(params: P): this {
    this._params = params;
    return this;
  }
  
  /**
   * Connects this node to another node with the "default" action
   * Shorthand for on("default", node)
   * 
   * @param node Successor node to connect
   * @returns The successor node for chaining
   */
  next<T extends BaseNode>(node: T): T {
    this.on("default", node);
    return node;
  }
  
  /**
   * Connects this node to another node with a specific action
   * 
   * @param action Action name that triggers this connection
   * @param node Successor node to connect
   * @returns This node instance for chaining
   */
  on(action: Action, node: BaseNode): this {
    if (this._successors.has(action))
      console.warn(`Overwriting successor for action '${action}'`);
    this._successors.set(action, node);
    return this;
  }
  
  /**
   * Gets the next node based on an action name
   * 
   * @param action Action name to follow, defaults to "default"
   * @returns The next node or undefined if no matching successor
   */
  getNextNode(action: Action = "default"): BaseNode | undefined {
    const nextAction = action || "default",
      next = this._successors.get(nextAction);
    if (!next && this._successors.size > 0)
      console.warn(
        `Flow ends: '${nextAction}' not found in [${Array.from(
          this._successors.keys()
        )}]`
      );
    return next;
  }
  
  /**
   * Creates a deep clone of this node including params and successors
   * Used by Flow to ensure each node instance runs with its own state
   * 
   * @returns A new instance of this node with copied state
   */
  clone(): this {
    const clonedNode = Object.create(Object.getPrototypeOf(this));
    Object.assign(clonedNode, this);
    clonedNode._params = { ...this._params };
    clonedNode._successors = new Map(this._successors);
    return clonedNode;
  }
}

/**
 * Enhanced node with retry capabilities
 * 
 * Extends BaseNode with automatic retries for failed executions
 * and customizable fallback behavior.
 * 
 * @template S Type of the shared state passed between nodes
 * @template P Type of parameters specific to this node
 */
class Node<
  S = unknown,
  P extends NonIterableObject = NonIterableObject
> extends BaseNode<S, P> {
  /** Maximum number of retry attempts */
  maxRetries: number;
  
  /** Wait time in seconds between retries */
  wait: number;
  
  /** Current retry attempt counter */
  currentRetry: number = 0;
  
  /**
   * Creates a new Node with retry capabilities
   * 
   * @param maxRetries Maximum number of retry attempts (default: 1)
   * @param wait Wait time in seconds between retries (default: 0)
   */
  constructor(maxRetries: number = 1, wait: number = 0) {
    super();
    this.maxRetries = maxRetries;
    this.wait = wait;
  }
  
  /**
   * Fallback method called when all retries are exhausted
   * Override to provide custom error handling
   * 
   * @param prepRes Result from the prep phase
   * @param error Error that caused the execution to fail
   * @returns Fallback result or throws the error
   */
  async execFallback(prepRes: unknown, error: Error): Promise<unknown> {
    throw error;
  }
  
  /**
   * Enhanced execution with retry logic
   * Attempts execution up to maxRetries times with optional wait periods
   * 
   * @param prepRes Result from the prep phase
   * @returns Result of successful execution or fallback
   */
  async _exec(prepRes: unknown): Promise<unknown> {
    for (
      this.currentRetry = 0;
      this.currentRetry < this.maxRetries;
      this.currentRetry++
    ) {
      try {
        return await this.exec(prepRes);
      } catch (e) {
        if (this.currentRetry === this.maxRetries - 1)
          return await this.execFallback(prepRes, e as Error);
        if (this.wait > 0)
          await new Promise((resolve) => setTimeout(resolve, this.wait * 1000));
      }
    }
    return undefined;
  }
}

/**
 * Node that processes batches of items sequentially
 * 
 * Extends Node to handle arrays of inputs, processing each item
 * one at a time and collecting the results.
 * 
 * @template S Type of the shared state passed between nodes
 * @template P Type of parameters specific to this node
 */
class BatchNode<
  S = unknown,
  P extends NonIterableObject = NonIterableObject
> extends Node<S, P> {
  /**
   * Processes each item in the input array sequentially
   * 
   * @param items Array of items to process
   * @returns Array of results in the same order as inputs
   */
  async _exec(items: unknown[]): Promise<unknown[]> {
    if (!items || !Array.isArray(items)) return [];
    const results = [];
    for (const item of items) results.push(await super._exec(item));
    return results;
  }
}

/**
 * Node that processes batches of items in parallel
 * 
 * Similar to BatchNode but processes all items concurrently
 * using Promise.all for better performance with I/O-bound tasks.
 * 
 * @template S Type of the shared state passed between nodes
 * @template P Type of parameters specific to this node
 */
class ParallelBatchNode<
  S = unknown,
  P extends NonIterableObject = NonIterableObject
> extends Node<S, P> {
  /**
   * Processes all items in the input array concurrently
   * 
   * @param items Array of items to process
   * @returns Array of results in the same order as inputs
   */
  async _exec(items: unknown[]): Promise<unknown[]> {
    if (!items || !Array.isArray(items)) return [];
    return Promise.all(items.map((item) => super._exec(item)));
  }
}

/**
 * Orchestrates the execution of a graph of nodes
 * 
 * Flow manages the execution of connected nodes, following the
 * actions returned by each node to determine the execution path.
 * 
 * @template S Type of the shared state passed between nodes
 * @template P Type of parameters specific to this flow
 */
class Flow<
  S = unknown,
  P extends NonIterableObject = NonIterableObject
> extends BaseNode<S, P> {
  /** Starting node of the flow */
  start: BaseNode;
  
  /**
   * Creates a new Flow with a specified starting node
   * 
   * @param start The first node to execute in the flow
   */
  constructor(start: BaseNode) {
    super();
    this.start = start;
  }
  
  /**
   * Internal method that orchestrates the execution of nodes
   * Follows the chain of nodes based on returned actions until completion
   * 
   * @param shared Shared state object passed between nodes
   * @param params Optional parameters to override flow params
   */
  protected async _orchestrate(shared: S, params?: P): Promise<void> {
    let current: BaseNode | undefined = this.start.clone();
    const p = params || this._params;
    while (current) {
      current.setParams(p);
      const action = await current._run(shared);
      current = current.getNextNode(action);
      current = current?.clone();
    }
  }
  
  /**
   * Runs the complete flow lifecycle
   * 
   * @param shared Shared state object passed between nodes
   * @returns Action name from the post phase
   */
  async _run(shared: S): Promise<Action | undefined> {
    const pr = await this.prep(shared);
    await this._orchestrate(shared);
    return await this.post(shared, pr, undefined);
  }
  
  /**
   * Execution is not supported on Flow directly
   * Flows orchestrate other nodes but don't have their own execution logic
   */
  async exec(prepRes: unknown): Promise<unknown> {
    throw new Error("Flow can't exec.");
  }
}

/**
 * Flow that runs the same graph multiple times with different parameters
 * 
 * BatchFlow extends Flow to run the same graph for each set of parameters
 * returned by the prep phase, processing items sequentially.
 * 
 * @template S Type of the shared state passed between nodes
 * @template P Type of base parameters for all batch runs
 * @template NP Type of the array of parameter objects for each run
 */
class BatchFlow<
  S = unknown,
  P extends NonIterableObject = NonIterableObject,
  NP extends NonIterableObject[] = NonIterableObject[]
> extends Flow<S, P> {
  /**
   * Runs the flow multiple times with different parameters
   * 
   * @param shared Shared state object passed between nodes
   * @returns Action name from the post phase
   */
  async _run(shared: S): Promise<Action | undefined> {
    const batchParams = await this.prep(shared);
    for (const bp of batchParams) {
      const mergedParams = { ...this._params, ...bp };
      await this._orchestrate(shared, mergedParams);
    }
    return await this.post(shared, batchParams, undefined);
  }
  
  /**
   * Override to return an array of parameter objects
   * Each object will be used for one execution of the flow
   * 
   * @param shared Shared state object
   * @returns Array of parameter objects for batch execution
   */
  async prep(shared: S): Promise<NP> {
    const empty: readonly NonIterableObject[] = [];
    return empty as NP;
  }
}

/**
 * Flow that runs the same graph multiple times in parallel
 * 
 * Similar to BatchFlow but processes all parameter sets concurrently
 * using Promise.all for better performance with I/O-bound tasks.
 * 
 * @template S Type of the shared state passed between nodes
 * @template P Type of base parameters for all batch runs
 * @template NP Type of the array of parameter objects for each run
 */
class ParallelBatchFlow<
  S = unknown,
  P extends NonIterableObject = NonIterableObject,
  NP extends NonIterableObject[] = NonIterableObject[]
> extends BatchFlow<S, P, NP> {
  /**
   * Runs the flow multiple times in parallel with different parameters
   * 
   * @param shared Shared state object passed between nodes
   * @returns Action name from the post phase
   */
  async _run(shared: S): Promise<Action | undefined> {
    const batchParams = await this.prep(shared);
    await Promise.all(
      batchParams.map((bp) => {
        const mergedParams = { ...this._params, ...bp };
        return this._orchestrate(shared, mergedParams);
      })
    );
    return await this.post(shared, batchParams, undefined);
  }
}

export {
  BaseNode,
  Node,
  BatchNode,
  ParallelBatchNode,
  Flow,
  BatchFlow,
  ParallelBatchFlow,
};


export * from './utils/monitoring';