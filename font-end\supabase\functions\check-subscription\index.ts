import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

// Helper logging function for enhanced debugging
const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : "";
  console.log(`[CHECK-SUBSCRIPTION] ${step}${detailsStr}`);
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  // Use the service role key to perform writes (upsert) in Supabase
  const supabaseClient = createClient(
    Deno.env.get("SUPABASE_URL") ?? "",
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    { auth: { persistSession: false } }
  );

  try {
    logStep("Function started");

    const stripeKey = Deno.env.get("STRIPE_SECRET_KEY");
    if (!stripeKey) throw new Error("STRIPE_SECRET_KEY is not set");
    logStep("Stripe key verified");

    const authHeader = req.headers.get("Authorization");
    if (!authHeader) throw new Error("No authorization header provided");
    logStep("Authorization header found");

    const token = authHeader.replace("Bearer ", "");
    logStep("Authenticating user with token");

    const { data: userData, error: userError } =
      await supabaseClient.auth.getUser(token);
    if (userError)
      throw new Error(`Authentication error: ${userError.message}`);
    const user = userData.user;
    if (!user?.email)
      throw new Error("User not authenticated or email not available");
    logStep("User authenticated", { userId: user.id, email: user.email });

    const stripe = new Stripe(stripeKey, { apiVersion: "2023-10-16" });
    const customers = await stripe.customers.list({
      email: user.email,
      limit: 1,
    });

    if (customers.data.length === 0) {
      logStep("No customer found, updating unsubscribed state");
      await supabaseClient.from("subscribers").upsert(
        {
          email: user.email,
          user_id: user.id,
          stripe_customer_id: null,
          subscribed: false,
          subscription_tier: null,
          subscription_end: null,
          updated_at: new Date().toISOString(),
        },
        { onConflict: "email" }
      );
      return new Response(
        JSON.stringify({
          subscribed: false,
          subscription_tier: null,
          subscription_end: null,
          trial_end: null,
          in_trial: false,
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 200,
        }
      );
    }

    const customerId = customers.data[0].id;
    logStep("Found Stripe customer", { customerId });

    const subscriptions = await stripe.subscriptions.list({
      customer: customerId,
      limit: 10,
    });

    let hasActiveSub = false;
    let subscriptionTier = null;
    let subscriptionEnd = null;
    let trialEnd = null;
    let inTrial = false;
    let stripeSubscriptionId = null;
    let currentPeriodStart = null;
    let currentPeriodEnd = null;

    let subscriptionStatus = null;

    // Check for active subscriptions or trials
    for (const subscription of subscriptions.data) {
      if (
        subscription.status === "active" ||
        subscription.status === "trialing"
      ) {

        subscriptionStatus = subscription.status;
        hasActiveSub = true;
        stripeSubscriptionId = subscription.id;
        currentPeriodStart = new Date(
          subscription.current_period_start * 1000
        ).toISOString();
        currentPeriodEnd = new Date(
          subscription.current_period_end * 1000
        ).toISOString();
        subscriptionEnd = currentPeriodEnd;

        // Check if in trial
        if (subscription.status === "trialing" && subscription.trial_end) {
          inTrial = true;
          trialEnd = new Date(subscription.trial_end * 1000).toISOString();
        }

        logStep("Active subscription found", {
          subscriptionId: subscription.id,
          status: subscriptionStatus,
          currentPeriodStart: currentPeriodStart,
          currentPeriodEnd: currentPeriodEnd,
          trialEnd: trialEnd,
          trial: inTrial,
        });

        // Determine subscription tier from price
        const priceId = subscription.items.data[0].price.id;
        const price = await stripe.prices.retrieve(priceId);
        const amount = price.unit_amount || 0;
        if (amount <= 999) {
          subscriptionTier = "Spark";
        } else if (amount <= 1999) {
          subscriptionTier = "Propel";
        } else {
          subscriptionTier = "Apex";
        }
        logStep("Determined subscription tier", {
          priceId,
          amount,
          subscriptionTier,
        });
        break; // Take the first active/trialing subscription
      }
    }

    if (!hasActiveSub) {
      logStep("No active subscription found");
    }

    // Update subscribers table
    await supabaseClient.from("subscribers").upsert(
      {
        email: user.email,
        user_id: user.id,
        stripe_customer_id: customerId,
        subscribed: hasActiveSub,
        subscription_tier: subscriptionTier,
        subscription_end: subscriptionEnd,
        trial_end: trialEnd,
        status: subscriptionStatus? subscriptionStatus : null,
        updated_at: new Date().toISOString(),
      },
      { onConflict: "email" }
    );

    // Get trial_tutorials_limit from tier_settings for the subscription tier
    let trialTutorialsLimit = 5; // Default value
    if (subscriptionTier) {
      const { data: tierSettings, error: tierError } = await supabaseClient
        .from('tier_settings')
        .select('trial_tutorials_limit')
        .eq('tier_name', subscriptionTier)
        .maybeSingle();
      
      if (tierError) {
        logStep("Error fetching tier settings", { error: tierError });
      } else if (tierSettings) {
        trialTutorialsLimit = tierSettings.trial_tutorials_limit || 5;
        logStep("Retrieved trial tutorials limit", { tier: subscriptionTier, limit: trialTutorialsLimit });
      }
    }

    // Update user_details with trial information and trial_tutorials_limit
    const { error: trialUpdateError } = await supabaseClient
      .from('user_details')
      .update({
        tier: subscriptionTier,
        trial_end_date: trialEnd,
        trial_tutorials_limit: trialTutorialsLimit,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);

    if (trialUpdateError) {
      logStep("Error updating trial information", { error: trialUpdateError });
    } else {
      logStep("Successfully updated trial information", { 
        trialEnd, 
        subscriptionTier, 
        trialTutorialsLimit 
      });
    }

    // Update user_details with billing cycle information if we have an active subscription
    if (
      hasActiveSub &&
      stripeSubscriptionId &&
      currentPeriodStart &&
      currentPeriodEnd
    ) {
      logStep("Updating billing cycle information", {
        stripeSubscriptionId,
        currentPeriodStart,
        currentPeriodEnd,
      });

      const { error: billingUpdateError } = await supabaseClient.rpc(
        "update_user_billing_cycle",
        {
          p_user_id: user.id,
          p_stripe_subscription_id: stripeSubscriptionId,
          p_current_period_start: currentPeriodStart,
          p_current_period_end: currentPeriodEnd,
        }
      );

      if (billingUpdateError) {
        logStep("Error updating billing cycle", { error: billingUpdateError });
      } else {
        logStep("Successfully updated billing cycle");
      }
    }

    logStep("Updated database with subscription info", {
      subscribed: hasActiveSub,
      subscriptionTier,
      inTrial,
      trialEnd,
    });

    // After all updates, verify data consistency
    const { data: verifyData, error: verifyError } = await supabaseClient.rpc(
      'sync_trial_status',
      { p_user_id: user.id }
    );
    
    if (verifyError) {
      logStep("Warning: Trial status sync verification failed", { error: verifyError });
    } else {
      logStep("Trial status sync verified", { inTrial: verifyData });
    }

    return new Response(
      JSON.stringify({
        subscribed: hasActiveSub,
        subscription_tier: subscriptionTier,
        subscription_end: subscriptionEnd,
        trial_end: trialEnd,
        in_trial: inTrial,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    // Enhanced error logging
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : undefined;
    logStep("ERROR in check-subscription", { 
      message: errorMessage,
      stack: errorStack,
      userId: user?.id,
      email: user?.email
    });
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
