import { User, Session } from "@supabase/supabase-js";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { supabase } from "@/integrations/supabase/client";
import { UserMetadata } from "@/types";

interface SubscriptionInfo {
  subscribed: boolean;
  subscription_tier?: string;
  subscription_end?: string;
  trial_end?: string;
  in_trial: boolean;
  monthlyTutorialsCreated: number;
  maxTutorialsPerMonth: number;
  currentBillingCycleStart?: string;
  currentBillingCycleEnd?: string;
}

interface Actions {
  setUser: (user: User | null) => void;
  setSession: (session: Session | null) => void;
  setLoading: (loading: boolean) => void;
  setIsAdmin: (isAdmin: boolean) => void;
  setSubscriptionInfo: (info: Partial<SubscriptionInfo>) => void;
  
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signUp: (email: string, password: string, metadata?: UserMetadata) => Promise<{ data: any; error: any }>;
  signInWithGoogle: () => Promise<{ data: any; error: any }>;
  signOut: () => Promise<{ error: any }>;
  
  checkAdminStatus: (userId: string) => Promise<void>;
  fetchSubscriptionInfo: (accessToken: string) => Promise<void>;
  fetchUsageLimits: (userId: string) => Promise<void>;
}

interface State {
  user: User | null;
  session: Session | null;
  loading: boolean;
  isAdmin: boolean;
  subscriptionInfo: SubscriptionInfo;
  
  // Convert getters to regular state properties
  isSignedIn: boolean;
  isLoaded: boolean;
  hasHydrated: boolean; // Add this to track when persistence has loaded
}

const useUserSessionStore = create(
  persist<State & Actions>(
    (set) => ({
      user: null,
      session: null,
      loading: true,
      isAdmin: false,
      hasHydrated: false,
      subscriptionInfo: {
        subscribed: false,
        in_trial: false,
        monthlyTutorialsCreated: 0,
        maxTutorialsPerMonth: 0,
        currentBillingCycleStart: undefined,
        currentBillingCycleEnd: undefined,
      },
      
      // Computed properties as state
      isSignedIn: false,
      isLoaded: false,
      
      setUser: (user) => {
        console.log("Setting user:", user);
        set({ 
          user,
          isSignedIn: !!user // Update isSignedIn when user changes
        });
      },
      
      setSession: (session) => {
        console.log("Setting session:", session);
        set({ 
          session,
          user: session?.user ?? null,
          isSignedIn: !!(session?.user) // Update both user and isSignedIn
        });
      },
      
      setLoading: (loading) => set({ 
        loading,
        isLoaded: !loading
      }),
      
      setIsAdmin: (isAdmin) => set({ isAdmin }),
      
      setSubscriptionInfo: (info) => set(state => ({ 
        subscriptionInfo: { ...state.subscriptionInfo, ...info } 
      })),

      signIn: async (email, password) => {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });
        return { data, error };
      },
      
      signUp: async (email, password, metadata) => {
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: metadata || {}
          }
        });
        return { data, error };
      },
      
      signInWithGoogle: async () => {
        // Get the current origin, which will be the production URL in production
        const redirectUrl = typeof window !== 'undefined' 
          ? `${window.location.origin}/xyz/auth` 
          : 'http://localhost:8080/xyz/auth';
        
          console.log("Redirecting to:", typeof window);
        const { data, error } = await supabase.auth.signInWithOAuth({
          provider: 'google',
          options: {
            redirectTo: redirectUrl,
          },
        });
        return { data, error };
      },
      
      signOut: async () => {
        const { error } = await supabase.auth.signOut();
        set({ 
          user: null, 
          session: null, 
          isAdmin: false,
          isSignedIn: false,
          subscriptionInfo: {
            subscribed: false,
            in_trial: false,
            monthlyTutorialsCreated: 0,
            maxTutorialsPerMonth: 0,
            currentBillingCycleStart: undefined,
            currentBillingCycleEnd: undefined,
          }
        });
        return { error };
      },
      
      checkAdminStatus: async (userId) => {
        try {
          const { data, error } = await supabase
            .from('user_details')
            .select('is_admin')
            .eq('id', userId)
            .single();

          if (error) {
            console.error('Error checking admin status:', error);
            set({ isAdmin: false });
          } else {
            set({ isAdmin: data?.is_admin || false });
          }
        } catch (error) {
          console.error('Error checking admin status:', error);
          set({ isAdmin: false });
        }
      },
      
      fetchSubscriptionInfo: async (accessToken) => {
        try {
          const { data, error } = await supabase.functions.invoke('check-subscription', {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          });

          if (error) throw error;
          
          set(state => ({ 
            subscriptionInfo: { 
              ...state.subscriptionInfo,
              subscribed: data.subscribed,
              subscription_tier: data.subscription_tier,
              subscription_end: data.subscription_end,
              trial_end: data.trial_end,
              in_trial: data.in_trial
            } 
          }));
        } catch (error) {
          console.error('Error fetching subscription info:', error);
        }
      },
      
      fetchUsageLimits: async (userId) => {
        try {
          const { data: userDetails, error: userError } = await supabase
            .from('user_details')
            .select(`
              monthly_tutorials_created,
              current_billing_cycle_start,
              current_billing_cycle_end,
              tier
            `)
            .eq('id', userId)
            .single();

          if (userError) throw userError;

          if (!userDetails.tier) {
            set(state => ({ 
              subscriptionInfo: { 
                ...state.subscriptionInfo,
                monthlyTutorialsCreated: userDetails.monthly_tutorials_created || 0,
                maxTutorialsPerMonth: 0,
                currentBillingCycleStart: userDetails.current_billing_cycle_start,
                currentBillingCycleEnd: userDetails.current_billing_cycle_end
              } 
            }));
            return;
          }

          const { data: tierSettings, error: tierError } = await supabase
            .from('tier_settings')
            .select('max_tutorials_per_month')
            .eq('tier_name', userDetails.tier)
            .single();

          if (tierError) throw tierError;
          
          set(state => ({ 
            subscriptionInfo: { 
              ...state.subscriptionInfo,
              monthlyTutorialsCreated: userDetails.monthly_tutorials_created || 0,
              maxTutorialsPerMonth: tierSettings.max_tutorials_per_month || 0,
              currentBillingCycleStart: userDetails.current_billing_cycle_start,
              currentBillingCycleEnd: userDetails.current_billing_cycle_end
            } 
          }));
        } catch (error) {
          console.error('Error fetching usage limits:', error);
        }
      },
    }),
    {
      name: "user-session",
      storage: createJSONStorage(() => localStorage),
      // Add partialize to exclude session from persistence (sessions shouldn't be persisted)
    partialize: (state) => ({
        ...state,
        session: null, // Never persist session for security
        loading: false, // Reset loading state on hydration
        hasHydrated: true
      }) as State & Actions,
      onRehydrateStorage: () => (state) => {
        // After rehydration, check if we have a user and update isSignedIn
        if (state) {
          state.hasHydrated = true;
          state.isSignedIn = !!state.user;
          console.log("State rehydrated, user:", state.user, "isSignedIn:", state.isSignedIn);
        }
      },
    }
  )
);

// Initialize auth state listener
if (typeof window !== 'undefined') {
  // Add a small delay to ensure store is initialized
  setTimeout(() => {
    // Set up auth state listener
    supabase.auth.onAuthStateChange((event, session) => {
      const store = useUserSessionStore.getState();
      
      console.log("Auth state changed", event, session);
      console.log("Current store state before update:", {
        user: store.user,
        isSignedIn: store.isSignedIn,
        hasHydrated: store.hasHydrated
      });
      
      // Always update session and user state from auth changes
      store.setSession(session);
      store.setUser(session?.user ?? null);
      store.setLoading(false);
      
      if (session?.user) {
        store.checkAdminStatus(session.user.id);
        store.fetchSubscriptionInfo(session.access_token);
        store.fetchUsageLimits(session.user.id);
      } else {
        store.setIsAdmin(false);
      }
      
      console.log("Store state after update:", {
        user: useUserSessionStore.getState().user,
        isSignedIn: useUserSessionStore.getState().isSignedIn
      });
    });

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      console.log("Initial session", session);
      const store = useUserSessionStore.getState();
      
      store.setSession(session);
      store.setUser(session?.user ?? null);
      store.setLoading(false);
      
      if (session?.user) {
        store.checkAdminStatus(session.user.id);
        store.fetchSubscriptionInfo(session.access_token);
        store.fetchUsageLimits(session.user.id);
      }
    });
  }, 0);
}

export default useUserSessionStore;
