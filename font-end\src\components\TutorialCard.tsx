
import React from "react";
import { Link } from "react-router-dom";
import { Book<PERSON><PERSON>, Clock, Eye, Trash2 } from "lucide-react";
import { Tutorial } from "@/hooks/useTutorials";

// Helper functions for time formatting
const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInHours = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60 * 60)
  );

  if (diffInHours < 1) return "just now";
  if (diffInHours === 1) return "1 hour ago";
  if (diffInHours < 24) return `${diffInHours} hours ago`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays === 1) return "yesterday";
  if (diffInDays < 7) return `${diffInDays} days ago`;

  return date.toLocaleDateString();
};

// Language to icon mapping
const languageIcons: Record<string, string> = {
  React: "fa-brands fa-react",
  Python: "fa-brands fa-python",
  "Node.js": "fa-brands fa-node-js",
  Go: "fa-brands fa-golang",
  PHP: "fa-brands fa-php",
  Java: "fa-brands fa-java",
  Angular: "fa-brands fa-angular",
  Redux: "fa-brands fa-react",
  Docker: "fa-brands fa-docker",
  Rust: "fa-brands fa-rust",
  GraphQL: "fa-solid fa-database",
  TypeScript: "fa-brands fa-js",
  JavaScript: "fa-brands fa-js",
};

interface TutorialCardProps {
  tutorial: Tutorial & { 
    type?: string;
    allTypes?: string[];
  };
  showDeleteButton?: boolean;
  onDeleteClick?: (e: React.MouseEvent, tutorial: Tutorial) => void;
}

const TutorialCard: React.FC<TutorialCardProps> = ({
  tutorial,
  showDeleteButton = false,
  onDeleteClick,
}) => {
  // Function to render multiple badges
  const renderBadges = () => {
    if (!tutorial.allTypes || tutorial.allTypes.length === 0) {
      // Fallback to single type badge
      if (tutorial.type === "featured") {
        return (
          <div className="absolute top-2 right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs px-2 py-1 rounded-full font-medium">
            ⭐ Featured
          </div>
        );
      }
      if (tutorial.type === "popular") {
        return (
          <div className="absolute top-2 right-2 bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
            🔥 Hot
          </div>
        );
      }
      return null;
    }

    return (
      <div className="absolute top-2 right-2 flex flex-col gap-1">
        {tutorial.allTypes.includes("featured") && (
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs px-2 py-1 rounded-full font-medium">
            ⭐ Featured
          </div>
        )}
        {tutorial.allTypes.includes("popular") && (
          <div className="bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
            🔥 Hot
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="relative group">
      <Link
        to={`/tutorial/${tutorial.id}`}
        className="block bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
      >
        <div
          className={`h-[140px] bg-gradient-to-r ${tutorial.backgroundColor} relative`}
        >
          <div className="absolute inset-0 flex items-center justify-center">
            <img
              className="object-fill"
              src={tutorial.imageSrc}
              alt={tutorial.title}
            />
          </div>

          {/* Multiple Type Badges */}
          {renderBadges()}

          <div className="absolute bottom-3 left-3 flex items-center">
            <i
              className={`${
                languageIcons[tutorial.language || ""] ||
                "fa-solid fa-code"
              } text-white text-lg mr-2`}
            ></i>
            <span className="text-white font-medium text-sm">
              {tutorial.language}
            </span>
          </div>
        </div>
        <div className="p-4">
          <h3 className="font-semibold text-gray-800 mb-2">
            {tutorial.title}
          </h3>
          <p className="text-gray-600 text-xs mb-3">
            {tutorial.description}
          </p>
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center text-gray-500">
              <BookOpen className="h-3 w-3 mr-1" />
              <span>{tutorial.chaptersCount} chapters</span>
            </div>
            <div className="flex items-center text-gray-500">
              {tutorial.views ? (
                <>
                  <Eye className="h-3 w-3 mr-1" />
                  <span>
                    {tutorial.views > 999
                      ? `${Math.floor(tutorial.views / 1000)}k`
                      : tutorial.views}
                  </span>
                </>
              ) : (
                <>
                  <Clock className="h-3 w-3 mr-1" />
                  <span>{formatTimeAgo(tutorial.createdAt)}</span>
                </>
              )}
            </div>
          </div>
        </div>
      </Link>

      {/* Delete Button - Only visible on hover and when showDeleteButton is true */}
      {showDeleteButton && onDeleteClick && (
        <button
          onClick={(e) => onDeleteClick(e, tutorial)}
          className="absolute top-2 left-2 p-2 rounded-full bg-white shadow-md text-red-500 hover:text-red-700 opacity-0 group-hover:opacity-100 transition-opacity z-10"
          title="Delete tutorial"
        >
          <Trash2 className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};

export default TutorialCard;
