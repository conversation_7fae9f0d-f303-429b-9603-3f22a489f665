
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useRegenerateCover } from "@/hooks/useRegenerateCover";

interface RegenerateCoverButtonProps {
  tutorialId: string;
  userId?: string;
  onSuccess?: () => void;
}

const RegenerateCoverButton = ({ 
  tutorialId, 
  userId, 
  onSuccess 
}: RegenerateCoverButtonProps) => {
  const { user } = useAuth();
  const { regenerateCover, isRegenerating } = useRegenerateCover();

  // Only show the button if user is logged in and owns the tutorial
  if (!user || (userId && user.id !== userId)) {
    return null;
  }

  const handleRegenerate = async () => {
    const success = await regenerateCover(tutorialId);
    if (success && onSuccess) {
      onSuccess();
    }
  };

  return (
    <Button
      onClick={handleRegenerate}
      disabled={isRegenerating}
      variant="outline"
      size="sm"
      className="flex items-center gap-2"
    >
      <RefreshCw className={`h-4 w-4 ${isRegenerating ? 'animate-spin' : ''}`} />
      {isRegenerating ? 'Regenerating...' : 'Regenerate Cover'}
    </Button>
  );
};

export default RegenerateCoverButton;
