
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { useTierSettings } from "@/hooks/useTierSettings";
import { Settings, DollarSign, Users, Shield, Plus, X, Star, Calendar } from "lucide-react";

const TierSettingsPanel = () => {
  const { tierSettings, loading, saving, updateTierSetting } = useTierSettings();
  const [editingTier, setEditingTier] = useState<string | null>(null);
  const [formData, setFormData] = useState<any>({});
  const [newFeature, setNewFeature] = useState('');

  const handleEdit = (tier: any) => {
    setEditingTier(tier.id);
    setFormData(tier);
  };

  const handleSave = async () => {
    if (!editingTier) return;
    
    await updateTierSetting(editingTier, formData);
    setEditingTier(null);
    setFormData({});
  };

  const handleCancel = () => {
    setEditingTier(null);
    setFormData({});
    setNewFeature('');
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addFeature = () => {
    if (!newFeature.trim()) return;
    
    const currentFeatures = formData.features || [];
    updateFormData('features', [...currentFeatures, newFeature.trim()]);
    setNewFeature('');
  };

  const removeFeature = (index: number) => {
    const currentFeatures = formData.features || [];
    const updatedFeatures = currentFeatures.filter((_, i) => i !== index);
    updateFormData('features', updatedFeatures);
  };

  const handleFeatureKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addFeature();
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Tier Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground">Loading tier settings...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Tier Settings Management
        </CardTitle>
        <CardDescription>
          Configure features, limits, and pricing for each tier
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue={tierSettings[0]?.tier_name.toLowerCase() || 'spark'} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            {tierSettings.map((tier) => (
              <TabsTrigger key={tier.id} value={tier.tier_name.toLowerCase()}>
                {tier.tier_name}
              </TabsTrigger>
            ))}
          </TabsList>

          {tierSettings.map((tier) => (
            <TabsContent key={tier.id} value={tier.tier_name.toLowerCase()} className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">{tier.tier_name} Tier</h3>
                  {editingTier === tier.id ? (
                    <div className="flex gap-2">
                      <Button onClick={handleSave} disabled={saving} size="sm">
                        {saving ? 'Saving...' : 'Save'}
                      </Button>
                      <Button onClick={handleCancel} variant="outline" size="sm">
                        Cancel
                      </Button>
                    </div>
                  ) : (
                    <Button onClick={() => handleEdit(tier)} size="sm">
                      Edit
                    </Button>
                  )}
                </div>

                <Separator />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Pricing Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      <h4 className="font-medium">Pricing</h4>
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor={`price-${tier.id}`}>Monthly Price ($)</Label>
                        <Input
                          id={`price-${tier.id}`}
                          type="number"
                          step="0.01"
                          value={editingTier === tier.id ? formData.price_monthly : tier.price_monthly}
                          onChange={(e) => updateFormData('price_monthly', parseFloat(e.target.value))}
                          disabled={editingTier !== tier.id}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Trial Configuration Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <h4 className="font-medium">Trial Configuration</h4>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label htmlFor={`has-trial-${tier.id}`}>Has Trial Period</Label>
                        <Switch
                          id={`has-trial-${tier.id}`}
                          checked={editingTier === tier.id ? formData.has_trial : tier.has_trial}
                          onCheckedChange={(checked) => updateFormData('has_trial', checked)}
                          disabled={editingTier !== tier.id}
                        />
                      </div>

                      <div>
                        <Label htmlFor={`trial-days-${tier.id}`}>Trial Days</Label>
                        <Input
                          id={`trial-days-${tier.id}`}
                          type="number"
                          value={editingTier === tier.id ? formData.trial_days : tier.trial_days}
                          onChange={(e) => updateFormData('trial_days', parseInt(e.target.value))}
                          disabled={editingTier !== tier.id}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Limits Section */}
                  <div className="space-y-4 md:col-span-2">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <h4 className="font-medium">Usage Limits</h4>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor={`tutorials-${tier.id}`}>Max Tutorials/Month</Label>
                        <Input
                          id={`tutorials-${tier.id}`}
                          type="number"
                          value={editingTier === tier.id ? formData.max_tutorials_per_month : tier.max_tutorials_per_month}
                          onChange={(e) => updateFormData('max_tutorials_per_month', parseInt(e.target.value))}
                          disabled={editingTier !== tier.id}
                          placeholder="-1 for unlimited"
                        />
                        <p className="text-sm text-muted-foreground">Use -1 for unlimited</p>
                      </div>

                      <div>
                        <Label htmlFor={`filesize-${tier.id}`}>Max File Size (MB)</Label>
                        <Input
                          id={`filesize-${tier.id}`}
                          type="number"
                          value={editingTier === tier.id ? formData.max_file_size_mb : tier.max_file_size_mb}
                          onChange={(e) => updateFormData('max_file_size_mb', parseInt(e.target.value))}
                          disabled={editingTier !== tier.id}
                          placeholder="-1 for unlimited"
                        />
                      </div>

                      <div>
                        <Label htmlFor={`trial-limit-${tier.id}`}>Trial Tutorials Limit</Label>
                        <Input
                          id={`trial-limit-${tier.id}`}
                          type="number"
                          value={editingTier === tier.id ? formData.trial_tutorials_limit : tier.trial_tutorials_limit}
                          onChange={(e) => updateFormData('trial_tutorials_limit', parseInt(e.target.value))}
                          disabled={editingTier !== tier.id}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Features Section */}
                  <div className="space-y-4 md:col-span-2">
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4" />
                      <h4 className="font-medium">Custom Features</h4>
                    </div>
                    
                    <div className="space-y-3">
                      {/* Feature List */}
                      <div className="space-y-2">
                        {(editingTier === tier.id ? formData.features || [] : tier.features || []).map((feature: string, index: number) => (
                          <div key={index} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded-md">
                            <span className="text-sm">{feature}</span>
                            {editingTier === tier.id && (
                              <Button
                                onClick={() => removeFeature(index)}
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                        ))}
                        
                        {((editingTier === tier.id ? formData.features || [] : tier.features || []).length === 0) && (
                          <p className="text-sm text-muted-foreground italic">No custom features added</p>
                        )}
                      </div>

                      {/* Add Feature Input (only when editing) */}
                      {editingTier === tier.id && (
                        <div className="flex gap-2">
                          <Input
                            placeholder="Enter feature name..."
                            value={newFeature}
                            onChange={(e) => setNewFeature(e.target.value)}
                            onKeyPress={handleFeatureKeyPress}
                            className="flex-1"
                          />
                          <Button onClick={addFeature} size="sm" disabled={!newFeature.trim()}>
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Boolean Features Section */}
                  <div className="space-y-4 md:col-span-2">
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      <h4 className="font-medium">System Features</h4>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor={`private-repos-${tier.id}`}>Private Repository Access</Label>
                        <Switch
                          id={`private-repos-${tier.id}`}
                          checked={editingTier === tier.id ? formData.can_access_private_repos : tier.can_access_private_repos}
                          onCheckedChange={(checked) => updateFormData('can_access_private_repos', checked)}
                          disabled={editingTier !== tier.id}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor={`priority-support-${tier.id}`}>Priority Support</Label>
                        <Switch
                          id={`priority-support-${tier.id}`}
                          checked={editingTier === tier.id ? formData.has_priority_support : tier.has_priority_support}
                          onCheckedChange={(checked) => updateFormData('has_priority_support', checked)}
                          disabled={editingTier !== tier.id}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor={`api-access-${tier.id}`}>API Access</Label>
                        <Switch
                          id={`api-access-${tier.id}`}
                          checked={editingTier === tier.id ? formData.has_api_access : tier.has_api_access}
                          onCheckedChange={(checked) => updateFormData('has_api_access', checked)}
                          disabled={editingTier !== tier.id}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor={`custom-branding-${tier.id}`}>Custom Branding</Label>
                        <Switch
                          id={`custom-branding-${tier.id}`}
                          checked={editingTier === tier.id ? formData.has_custom_branding : tier.has_custom_branding}
                          onCheckedChange={(checked) => updateFormData('has_custom_branding', checked)}
                          disabled={editingTier !== tier.id}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default TierSettingsPanel;
