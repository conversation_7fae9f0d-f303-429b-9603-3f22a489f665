import { Flow } from '../index';
import * as fs from 'fs';
import * as path from 'path';

// Import necessary visualization libraries
import { createCanvas } from 'canvas';

/**
 * Options for diagram generation
 */
export interface DiagramOptions {
  width?: number;
  height?: number;
  backgroundColor?: string;
  nodeColor?: string;
  edgeColor?: string;
  fontSize?: number;
  padding?: number;
}

/**
 * Extended Flow class with diagram export capabilities
 */
export class DiagramFlow<S = unknown, P extends Record<string, any> = Record<string, any>> extends Flow<S, P> {
  /**
   * Export the flow structure as a PNG diagram
   * 
   * @param filename Path where the diagram should be saved
   * @param options Customization options for the diagram
   */
  async export_png(filename: string, options?: DiagramOptions): Promise<void> {
    const opts = {
      width: options?.width || 1200,
      height: options?.height || 800,
      backgroundColor: options?.backgroundColor || '#f5f5f5',
      nodeColor: options?.nodeColor || '#e1f5fe',
      edgeColor: options?.edgeColor || '#333333',
      fontSize: options?.fontSize || 14,
      padding: options?.padding || 40
    };

    // Ensure output directory exists
    const dir = path.dirname(filename);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Create canvas
    const canvas = createCanvas(opts.width, opts.height);
    const ctx = canvas.getContext('2d');

    // Fill background
    ctx.fillStyle = opts.backgroundColor;
    ctx.fillRect(0, 0, opts.width, opts.height);

    // Analyze flow structure
    const nodes = this.collectNodes();
    const edges = this.collectEdges(nodes);

    // Position nodes
    const positions = this.calculateNodePositions(nodes, opts);

    // Draw edges
    this.drawEdges(ctx, edges, positions, opts);

    // Draw nodes
    this.drawNodes(ctx, nodes, positions, opts);

    // Save to file
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync(filename, buffer);

    console.log(`Flow diagram exported to ${filename}`);
  }

  /**
   * Collect all nodes in the flow
   */
  private collectNodes(): Map<string, any> {
    const nodes = new Map();
    const visited = new Set();

    const traverse = (node: any, id: string) => {
      if (visited.has(node)) return;
      visited.add(node);
      
      nodes.set(id, {
        node,
        name: node.constructor.name,
        id
      });

      // Process successors
      if (node._successors) {
        let idx = 0;
        node._successors.forEach((successor: any, action: string) => {
          const nextId = `${id}_${action || 'default'}_${idx++}`;
          traverse(successor, nextId);
        });
      }
    };

    traverse(this.start, 'start');
    return nodes;
  }

  /**
   * Collect all edges between nodes
   */
  private collectEdges(nodes: Map<string, any>): Array<{from: string, to: string, label: string}> {
    const edges: Array<{from: string, to: string, label: string}> = [];
    const visited = new Set();

    const traverse = (nodeId: string) => {
      const nodeInfo = nodes.get(nodeId);
      if (!nodeInfo || visited.has(nodeId)) return;
      visited.add(nodeId);

      const node = nodeInfo.node;
      if (node._successors) {
        let idx = 0;
        node._successors.forEach((successor: any, action: string) => {
          const targetId = `${nodeId}_${action || 'default'}_${idx++}`;
          if (nodes.has(targetId)) {
            edges.push({
              from: nodeId,
              to: targetId,
              label: action || 'default'
            });
            traverse(targetId);
          }
        });
      }
    };

    traverse('start');
    return edges;
  }

  /**
   * Calculate positions for each node
   */
  private calculateNodePositions(nodes: Map<string, any>, options: Required<DiagramOptions>): Map<string, {x: number, y: number}> {
    const positions = new Map();
    const nodeArray = Array.from(nodes.values());
    
    // Simple layout algorithm - position nodes in a grid
    const cols = Math.ceil(Math.sqrt(nodeArray.length));
    const nodeWidth = (options.width - 2 * options.padding) / cols;
    const nodeHeight = 80;
    const rows = Math.ceil(nodeArray.length / cols);
    
    nodeArray.forEach((node, idx) => {
      const col = idx % cols;
      const row = Math.floor(idx / cols);
      
      positions.set(node.id, {
        x: options.padding + col * nodeWidth + nodeWidth / 2,
        y: options.padding + row * nodeHeight * 2 + nodeHeight / 2
      });
    });
    
    return positions;
  }

  /**
   * Draw nodes on the canvas
   */
  private drawNodes(
    ctx: any, 
    nodes: Map<string, any>, 
    positions: Map<string, {x: number, y: number}>,
    options: Required<DiagramOptions>
  ): void {
    ctx.font = `${options.fontSize}px Arial`;
    
    nodes.forEach((nodeInfo) => {
      const pos = positions.get(nodeInfo.id);
      if (!pos) return;
      
      const nodeWidth = 160;
      const nodeHeight = 60;
      
      // Draw node box
      ctx.fillStyle = options.nodeColor;
      ctx.strokeStyle = options.edgeColor;
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.roundRect(pos.x - nodeWidth/2, pos.y - nodeHeight/2, nodeWidth, nodeHeight, 10);
      ctx.fill();
      ctx.stroke();
      
      // Draw node name
      ctx.fillStyle = '#000000';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(nodeInfo.name, pos.x, pos.y);
    });
  }

  /**
   * Draw edges between nodes
   */
  private drawEdges(
    ctx: any,
    edges: Array<{from: string, to: string, label: string}>,
    positions: Map<string, {x: number, y: number}>,
    options: Required<DiagramOptions>
  ): void {
    ctx.strokeStyle = options.edgeColor;
    ctx.fillStyle = options.edgeColor;
    ctx.lineWidth = 2;
    ctx.font = `${options.fontSize - 2}px Arial`;
    
    edges.forEach(edge => {
      const fromPos = positions.get(edge.from);
      const toPos = positions.get(edge.to);
      
      if (!fromPos || !toPos) return;
      
      // Draw line
      ctx.beginPath();
      ctx.moveTo(fromPos.x, fromPos.y + 30);
      ctx.lineTo(toPos.x, toPos.y - 30);
      ctx.stroke();
      
      // Draw arrow
      const angle = Math.atan2(toPos.y - fromPos.y, toPos.x - fromPos.x);
      const arrowSize = 10;
      
      ctx.beginPath();
      ctx.moveTo(
        toPos.x - arrowSize * Math.cos(angle - Math.PI/6),
        toPos.y - 30 - arrowSize * Math.sin(angle - Math.PI/6)
      );
      ctx.lineTo(toPos.x, toPos.y - 30);
      ctx.lineTo(
        toPos.x - arrowSize * Math.cos(angle + Math.PI/6),
        toPos.y - 30 - arrowSize * Math.sin(angle + Math.PI/6)
      );
      ctx.closePath();
      ctx.fill();
      
      // Draw label
      const midX = (fromPos.x + toPos.x) / 2;
      const midY = (fromPos.y + 30 + toPos.y - 30) / 2;
      
      ctx.fillStyle = '#ffffff';
      const textWidth = ctx.measureText(edge.label).width + 10;
      ctx.fillRect(midX - textWidth/2, midY - 10, textWidth, 20);
      
      ctx.fillStyle = options.edgeColor;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(edge.label, midX, midY);
    });
  }
}