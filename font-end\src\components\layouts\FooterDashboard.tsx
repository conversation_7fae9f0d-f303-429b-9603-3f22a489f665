import { Link } from "react-router-dom";
export function FooterDashboard() {
  return       <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <div className="flex items-center">
                <i className="fa-solid fa-book-open text-tutorial-primary text-xl mr-2"></i>
                <span className="text-lg font-bold text-gray-800">
                  CodeTutor
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                Automated tutorials from GitHub repositories
              </p>
            </div>

            <div className="flex space-x-6">
              <a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-tutorial-primary"
              >
                <i className="fa-brands fa-github text-lg"></i>
              </a>
              <a
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-tutorial-primary"
              >
                <i className="fa-brands fa-twitter text-lg"></i>
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-gray-500 hover:text-tutorial-primary"
              >
                <i className="fa-solid fa-envelope text-lg"></i>
              </a>
            </div>
          </div>

          <div className="border-t border-gray-200 mt-6 pt-6 flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm text-gray-600">
              © 2025 CodeTutor. All rights reserved.
            </div>
            <div className="mt-4 md:mt-0 flex space-x-6">
              <Link
                to="#"
                className="text-sm text-gray-600 hover:text-tutorial-primary"
              >
                Privacy Policy
              </Link>
              <Link
                to="#"
                className="text-sm text-gray-600 hover:text-tutorial-primary"
              >
                Terms of Service
              </Link>
              <Link
                to="#"
                className="text-sm text-gray-600 hover:text-tutorial-primary"
              >
                Contact
              </Link>
            </div>
          </div>
        </div>
      </footer>
      
    ;
}