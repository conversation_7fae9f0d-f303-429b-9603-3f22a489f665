
import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Clock, Zap, AlertTriangle } from 'lucide-react';
import { useTrialStatus } from '@/hooks/useTrialStatus';
import { useNavigate } from 'react-router-dom';

const TrialStatusBanner = () => {
  const { trialStatus, loading } = useTrialStatus();
  const navigate = useNavigate();

  if (loading || !trialStatus.isInTrial) return null;

  const remainingTutorials = trialStatus.tutorialsLimit - trialStatus.tutorialsCreated;
  const isNearLimit = remainingTutorials <= 1;
  const isExpiringSoon = trialStatus.daysLeft <= 2;

  const handleUpgradeClick = () => {
    navigate('/dashboard/subscription');
  };

  return (
    <Alert className={`mb-6 ${isNearLimit || isExpiringSoon ? 'border-orange-200 bg-orange-50' : 'border-blue-200 bg-blue-50'}`}>
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center space-x-3">
          {isNearLimit || isExpiringSoon ? (
            <AlertTriangle className="h-5 w-5 text-orange-600" />
          ) : (
            <Zap className="h-5 w-5 text-blue-600" />
          )}
          <div>
            <AlertDescription className="text-sm">
              <div className="flex items-center space-x-2 mb-1">
                <span className="font-medium">Trial Account</span>
                <Badge variant="outline" className="text-xs">
                  <Clock className="h-3 w-3 mr-1" />
                  {trialStatus.daysLeft} days left
                </Badge>
              </div>
              <div className="text-gray-600">
                {remainingTutorials > 0 ? (
                  <span>
                    {remainingTutorials} of {trialStatus.tutorialsLimit} tutorials remaining
                  </span>
                ) : (
                  <span className="text-orange-600 font-medium">
                    Trial limit reached - Upgrade to create more tutorials
                  </span>
                )}
              </div>
            </AlertDescription>
          </div>
        </div>
        {(isNearLimit || isExpiringSoon || remainingTutorials === 0) && (
          <Button size="sm" className="bg-primary-600 hover:bg-primary-700" onClick={handleUpgradeClick}>
            Upgrade Now
          </Button>
        )}
      </div>
    </Alert>
  );
};

export default TrialStatusBanner;
