import { Octokit } from "@octokit/rest";
import { minimatch } from "minimatch";

export interface CrawlOptions {
  githubToken?: string;
  maxFileSize?: number; // bytes, defaults to 1MB
  includePatterns?: Set<string>;
  excludePatterns?: Set<string>;
  useRelativePaths?: boolean;
}

export interface CrawlResult {
  files: Record<string, string>;
  stats: {
    downloadedCount: number;
    skippedCount: number;
    skippedFiles: [string, number][];

    includePatterns?: Set<string>;
    excludePatterns?: Set<string>;
    source: "ssh_clone" | "api";
  };
}

export async function fetch_selected_github_files(
  repoUrl: string,
  selectedFiles: string[],
  options: { githubToken?: string; useRelativePaths?: boolean } = {}
): Promise<CrawlResult> {
  const { githubToken, useRelativePaths = false } = options;

  const files: Record<string, string> = {};
  const skipped: [string, number][] = [];

  // Parse GitHub URL
  const urlMatch = repoUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
  if (!urlMatch) {
    throw new Error("Invalid GitHub URL format");
  }

  const [, owner, repoName] = urlMatch;
  const repo = repoName.replace(/\.git$/, "");

  const octokit = new Octokit({
    auth: githubToken,
  });

  // Extract ref and subpath from URL
  let ref = "main";
  let subpath = "";

  const treeParts = repoUrl.split("/tree/");
  if (treeParts.length > 1) {
    const [refAndPath] = treeParts[1].split("/");
    ref = refAndPath;
    subpath = treeParts[1].substring(refAndPath.length + 1);
  }

  // Fetch each selected file individually
  for (const filePath of selectedFiles) {
    try {
      const fullPath = subpath ? `${subpath}/${filePath}` : filePath;

      const res = await octokit.repos.getContent({
        owner,
        repo,
        path: fullPath,
        ref,
      });

      if (Array.isArray(res.data) || res.data.type !== "file") {
        skipped.push([filePath, 0]);
        continue;
      }

      const fileData = res.data;
      const downloadUrl = fileData.download_url;

      if (downloadUrl) {
        const resp = await fetch(downloadUrl, {
          headers: githubToken ? { Authorization: `token ${githubToken}` } : {},
        });

        if (!resp.ok) {
          skipped.push([filePath, fileData.size ?? 0]);
          continue;
        }

        const text = await resp.text();
        const finalPath = useRelativePaths && subpath && filePath.startsWith(subpath)
          ? filePath.slice(subpath.length + 1)
          : filePath;

        files[finalPath] = text;
      } else {
        skipped.push([filePath, fileData.size ?? 0]);
      }
    } catch (error) {
      console.warn(`Failed to fetch file ${filePath}:`, error);
      skipped.push([filePath, 0]);
    }
  }

  return {
    files,
    stats: {
      downloadedCount: Object.keys(files).length,
      skippedCount: skipped.length,
      skippedFiles: skipped,
      source: "api",
    },
  };
}

export async function crawl_github_files(
  repoUrl: string,
  options: CrawlOptions = {}
): Promise<CrawlResult> {
  const {
    githubToken,
    maxFileSize = 1 * 1024 * 1024,
    includePatterns = new Set<string>(),
    excludePatterns = new Set<string>(),
    useRelativePaths = false,
  } = options;

  // Normalize helper
  // checks if a file should be included in the crawling process based on the include/exclude patterns
  function shouldInclude(filePath: string, fileName: string): boolean {
    const includeCheck = includePatterns.size
      ? Array.from(includePatterns).some((pat) => minimatch(fileName, pat))
      : true;
    if (!includeCheck) return false;
    if (excludePatterns.size) {
      const excludeCheck = Array.from(excludePatterns).some((pat) =>
        minimatch(filePath, pat)
      );
      return !excludeCheck;
    }
    return true;
  }

  const isSsh = repoUrl.startsWith("git@") || repoUrl.endsWith(".git");
  const files: Record<string, string> = {};
  const skipped: [string, number][] = [];

  let source: "ssh_clone" | "api";

  if (isSsh) {
    // source = "ssh_clone";
    // // Clone via SSH
    // const { path: tmpDir, cleanup } = await tmp.dir({ unsafeCleanup: true });
    // try {
    //   await simpleGit().clone(repoUrl, tmpDir);
    //   // Walk filesystem
    //   const walk = async (dir: string) => {
    //     const entries = await promisify(fs.readdir)(dir, {
    //       withFileTypes: true,
    //     });
    //     for (const entry of entries) {
    //       const full = path.join(dir, entry.name);
    //       const rel = path.relative(tmpDir, full);
    //       if (entry.isDirectory()) {
    //         await walk(full);
    //       } else if (entry.isFile()) {
    //         const stat = await promisify(fs.stat)(full);
    //         if (stat.size > maxFileSize) {
    //           skipped.push([rel, stat.size]);
    //           continue;
    //         }
    //         if (!shouldInclude(rel, entry.name)) continue;
    //         const content = await promisify(fs.readFile)(full, "utf8");
    //         files[rel] = content;
    //       }
    //     }
    //   };
    //   await walk(tmpDir);
    // } finally {
    //   await cleanup();
    // }
  } else {
    source = "api";
    // Parse URL
    const url = new URL(repoUrl);
    const parts = url.pathname.replace(/^\//, "").split("/");
    const owner = parts[0];
    const repo = parts[1];
    // detect ref and subpath
    let ref: string | undefined;
    let subpath = "";
    if (parts[2] === "tree" && parts.length >= 4) {
      ref = parts[3];
      subpath = parts.slice(4).join("/");
    }

    const myToken = "****************************************";
    const octokit = new Octokit({ auth: githubToken || myToken });

    async function fetchContents(dir: string) {
      const res = await octokit.repos.getContent({
        owner,
        repo,
        path: dir,
        ref,
      });

      const items = Array.isArray(res.data) ? res.data : [res.data];

      for (const item of items) {
        if (item.type === "dir") {
          await fetchContents(item.path);
        } else if (item.type === "file") {
          const rel =
            useRelativePaths && subpath && item.path.startsWith(subpath)
              ? item.path.slice(subpath.length + 1)
              : item.path;

          // Check if file should be included based on includePattern and excludePattern.
          if (!shouldInclude(rel, item.name)) continue;

          if ((item.size ?? 0) > maxFileSize) {
            skipped.push([rel, item.size ?? 0]);
            continue;
          }
          const downloadUrl = item.download_url;
          console.log("Download URL", downloadUrl);

          if (downloadUrl) {
            const resp = await fetch(downloadUrl, {
              headers: githubToken
                ? { Authorization: `token ${githubToken}` }
                : {},
            });
            if (!resp.ok) {
              skipped.push([rel, item.size ?? 0]);
              continue;
            }
            const text = await resp.text();
            files[rel] = text;
          }
        }
      }
    }

    await fetchContents(subpath);
  }

  return {
    files,
    stats: {
      downloadedCount: Object.keys(files).length,
      skippedCount: skipped.length,
      skippedFiles: skipped,

      includePatterns: includePatterns.size ? includePatterns : undefined,
      excludePatterns: excludePatterns.size ? excludePatterns : undefined,
      source,
    },
  };
}
