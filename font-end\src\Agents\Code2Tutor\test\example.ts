// src/Agents/Code2Tutor/test/example.ts

/**
 * Example usage of the Code2Tutor agent
 * 
 * This file demonstrates how to use the Code2Tutor agent to transform
 * a code repository into an educational tutorial.
 */

import { 
  executeCode2TutorFlow, 
  createDefaultSharedStore,
  validateSharedStore,
  tutorEvents,
  TutorEventType
} from '../index';

/**
 * Example: Create a tutorial from a GitHub repository
 */
export async function createTutorialFromRepo() {
  // Create shared store with configuration
  const shared = createDefaultSharedStore({
    user_id: 'example-user',
    repo_url: 'https://github.com/example/simple-react-app',
    project_name: 'React Basics Tutorial',
    target_audience: 'beginner',
    content_language: 'english',
    tutorial_format: 'guided',
    include_exercises: true,
    include_diagrams: true,
    include_examples: true,
    max_concepts: 6,
    selected_files: [
      'src/App.js',
      'src/components/Header.js',
      'src/components/TodoList.js',
      'src/hooks/useTodos.js',
      'package.json',
      'README.md'
    ]
  });

  // Validate configuration
  const validationErrors = validateSharedStore(shared);
  if (validationErrors.length > 0) {
    console.error('Configuration errors:', validationErrors);
    return;
  }

  // Set up event listeners for monitoring
  const progressListener = tutorEvents.on(TutorEventType.PROGRESS, (event) => {
    console.log(`📈 Progress: ${event.progress}% - ${event.message}`);
  });

  const agentStatusListener = tutorEvents.on(TutorEventType.AGENT_STATUS, (event) => {
    console.log(`🤖 ${event.agentName}: ${event.status} - ${event.message}`);
  });

  const conceptListener = tutorEvents.on(TutorEventType.CONCEPT_EXTRACTED, (event) => {
    console.log(`💡 Concept extracted: ${event.concept.name}`);
  });

  const sectionListener = tutorEvents.on(TutorEventType.SECTION_GENERATED, (event) => {
    console.log(`📝 Section generated: ${event.section.title}`);
  });

  const completeListener = tutorEvents.on(TutorEventType.COMPLETE, (event) => {
    if (event.success) {
      console.log(`✅ Tutorial completed successfully!`);
      console.log(`🔗 Tutorial URL: ${event.tutorialUrl}`);
    } else {
      console.error(`❌ Tutorial failed: ${event.message}`);
    }
  });

  try {
    console.log('🚀 Starting Code2Tutor workflow...');
    
    // Execute the workflow
    const results = await executeCode2TutorFlow(shared);
    
    console.log('📊 Workflow Results:', {
      totalSteps: results.length,
      successful: results.filter((r: any) => r.success).length,
      failed: results.filter((r: any) => !r.success).length
    });

    return results;

  } catch (error) {
    console.error('💥 Workflow execution failed:', error);
    throw error;
  } finally {
    // Clean up event listeners
    progressListener();
    agentStatusListener();
    conceptListener();
    sectionListener();
    completeListener();
  }
}

/**
 * Example: Create a tutorial from local files
 */
export async function createTutorialFromLocal() {
  const shared = createDefaultSharedStore({
    user_id: 'example-user',
    local_dir: '/path/to/local/project',
    project_name: 'Local Project Tutorial',
    target_audience: 'intermediate',
    content_language: 'english',
    tutorial_format: 'self-paced',
    include_exercises: true,
    include_diagrams: false,
    max_concepts: 8,
    selected_files: [
      'src/main.py',
      'src/utils.py',
      'src/models/user.py',
      'tests/test_main.py',
      'requirements.txt'
    ]
  });

  try {
    const results = await executeCode2TutorFlow(shared);
    return results;
  } catch (error) {
    console.error('Failed to create tutorial from local files:', error);
    throw error;
  }
}

/**
 * Example: Advanced configuration with custom settings
 */
export async function createAdvancedTutorial() {
  const shared = createDefaultSharedStore({
    user_id: 'advanced-user',
    repo_url: 'https://github.com/example/complex-app',
    project_name: 'Advanced Architecture Tutorial',
    target_audience: 'advanced',
    content_language: 'english',
    tutorial_format: 'interactive',
    include_exercises: true,
    include_diagrams: true,
    include_examples: true,
    max_concepts: 12,
    use_cache: true,
    selected_files: [
      'src/architecture/core.ts',
      'src/services/api.ts',
      'src/middleware/auth.ts',
      'src/utils/validation.ts',
      'src/types/index.ts',
      'docs/architecture.md'
    ]
  });

  // Monitor specific events
  tutorEvents.on(TutorEventType.CONCEPT_EXTRACTED, (event) => {
    console.log(`New concept: ${event.concept.name} (${event.concept.difficulty})`);
    console.log(`Prerequisites: ${event.concept.prerequisites.join(', ')}`);
  });

  try {
    const results = await executeCode2TutorFlow(shared);
    
    // Process results
    const finalResult = results[results.length - 1];
    if (finalResult.success) {
      console.log('Tutorial created successfully!');
      console.log('Tutorial ID:', finalResult.data.tutorialId);
      console.log('Tutorial URL:', finalResult.data.tutorialUrl);
    }

    return results;
  } catch (error) {
    console.error('Advanced tutorial creation failed:', error);
    throw error;
  }
}

/**
 * Utility function to demonstrate configuration validation
 */
export function validateConfiguration() {
  // Valid configuration
  const validConfig = createDefaultSharedStore({
    user_id: 'test-user',
    repo_url: 'https://github.com/test/repo',
    project_name: 'Test Project'
  });

  console.log('Valid config errors:', validateSharedStore(validConfig));

  // Invalid configuration
  const invalidConfig = createDefaultSharedStore({
    user_id: '', // Invalid: empty user_id
    target_audience: 'expert' as any, // Invalid: not a valid audience
    max_concepts: 20 // Invalid: too many concepts
  });

  console.log('Invalid config errors:', validateSharedStore(invalidConfig));
}

// Export for testing
export {
  createDefaultSharedStore,
  validateSharedStore,
  TutorEventType
};
