import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useDeleteTutorial } from "@/hooks/useDeleteTutorial";
import { Loader2 } from "lucide-react";

interface DeleteTutorialDialogProps {
  isOpen: boolean;
  onClose: () => void;
  tutorialId: string;
  tutorialTitle: string;
  onDeleted: () => void;
}

const DeleteTutorialDialog: React.FC<DeleteTutorialDialogProps> = ({
  isOpen,
  onClose,
  tutorialId,
  tutorialTitle,
  onDeleted,
}) => {
  const { deleteTutorial, isDeleting, error } = useDeleteTutorial();

  const handleDelete = async () => {
    await deleteTutorial(tutorialId, () => {
      onDeleted();
      onClose();
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Delete Tutorial</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete the tutorial "{tutorialTitle}"? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
            {error}
          </div>
        )}
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isDeleting}>
            Cancel
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              "Delete"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteTutorialDialog;
