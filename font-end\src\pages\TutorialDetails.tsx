import { useParams } from "react-router-dom";
import NavBar from "@/components/layouts/NavBar";
import TutorialViewer from "@/components/TutorialViewer";
import RegenerateCoverButton from "@/components/RegenerateCoverButton";
import GitHubRepoCard from "@/components/GitHubRepoCard";
import { useTutorialDetails } from "@/hooks/useTutorialDetails";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useEffect, useMemo, useState } from "react";
import { parseGitHubUrl } from "@/utils/github";
import { GitHubRepoInfoTutorialCard } from "@/types/github";
import { useFeaturedTutorials } from "@/hooks/useTutorials";

const TutorialDetailsSkeleton = () => (
  <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    <div className="mx-auto px-6 py-6 ">
      {/* Compact Header Section Skeleton */}
      {/* <div className="mb-6">
        <Skeleton className="h-48 lg:h-56 w-full mb-4 rounded-xl" />
        <div className="max-w-5xl mx-auto">
          <Skeleton className="h-20 w-full rounded-lg" />
        </div>
      </div> */}

      {/* Content Section Skeleton */}
      <div className="flex flex-col xl:flex-row gap-6">
        <div className="xl:w-[320px] flex-shrink-0">
          <Skeleton className="h-[calc(100vh-180px)] w-full rounded-xl" />
        </div>
        <div className="flex-1 min-w-0">
          <Skeleton className="h-[calc(100vh-180px)] w-full rounded-xl" />
        </div>
      </div>
    </div>
  </div>
);

const TutorialDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { tutorial, loading, error } = useTutorialDetails(id || "");
  const [refreshKey, setRefreshKey] = useState(0);

  // const [repoInfo, setRepoInfo] = useState<GitHubRepoInfoTutorialCard | null>(
  //   null
  // );
  // const [loadingInfo, setLoadingInfo] = useState(true);
  // const [errorRepo, setErrorRepo] = useState<string | null>(null);

  // Format date to relative time
  // const formatDate = (dateString: string) => {
  //   const date = new Date(dateString);
  //   const now = new Date();
  //   const diffTime = Math.abs(now.getTime() - date.getTime());
  //   const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  //   if (diffDays === 1) return "1 day ago";
  //   if (diffDays < 30) return `${diffDays} days ago`;
  //   if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  //   return `${Math.floor(diffDays / 365)} years ago`;
  // };

  // // Format number with K/M suffix
  // const formatNumber = (num: number) => {
  //   if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
  //   if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
  //   return num.toString();
  // };

  // useEffect(() => {
  //   const fetchRepoInfo = async () => {
  //     const parsed = parseGitHubUrl(tutorial.repoUrl);
  //     if (!parsed) {
  //       setErrorRepo("Invalid GitHub URL");
  //       setLoadingInfo(false);
  //       return;
  //     }

  //     try {
  //       const repoUrl = `https://api.github.com/repos/${parsed.owner}/${parsed.repo}`;
  //       console.log(repoUrl);
  //       const response = await fetch(
  //         `https://api.github.com/repos/${parsed.owner}/${parsed.repo}`
  //       );
  //       if (!response.ok) {
  //         throw new Error("Repository not found");
  //       }
  //       const data = await response.json();
  //       setRepoInfo(data);
  //     } catch (err) {
  //       setErrorRepo(
  //         err instanceof Error ? err.message : "Failed to fetch repository info"
  //       );
  //     } finally {
  //       setLoadingInfo(false);
  //     }
  //   };

  //   fetchRepoInfo();
  // }, [tutorial?.repoUrl]);

  const handleCoverRegenerated = () => {
    // Force a refresh of the tutorial data
    setRefreshKey((prev) => prev + 1);
    // Reload the page to see the new cover image
    window.location.reload();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <TutorialDetailsSkeleton />
      </div>
    );
  }

  if (error || !tutorial) {
    return (
      <div className="container mx-auto py-20 px-4 max-w-7xl">
        <div className="max-w-xl mx-auto text-center">
          <Alert variant="destructive" className="shadow-lg border-0">
            <AlertCircle className="h-5 w-5" />
            <AlertTitle className="text-lg font-semibold">
              Tutorial Not Found
            </AlertTitle>
            <AlertDescription className="text-base mt-2">
              {error ||
                "The tutorial you're looking for doesn't exist or has been removed."}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  const tutorialContent = {
    id: tutorial.id,
    title: tutorial.title,
    description: tutorial.description,
    repoUrl: tutorial.repoUrl,
    chapters: [
      {
        id: "project_cover",
        title: `${tutorial.title}`,
        content: `# ${tutorial.title}\n\n![${tutorial.title} cover](${
          tutorial.imageSrc
        })\n\n${
          tutorial.repoUrl
            ? `**Source Repository:** [${tutorial.repoUrl}](${tutorial.repoUrl})\n\n`
            : ""
        }`,
      },
      // {
      //   id: "project_cover",
      //   title: `${tutorial.title}`,
      //   content: `# ${tutorial.title}\n\n![${tutorial.title} cover](${
      //     tutorial.imageSrc
      //   })\n\n${
      //     tutorial.repoUrl && repoInfo ?
      //     `<div class="github-repo-card">\n\n` +
      //     `## [${repoInfo.full_name}](${repoInfo.html_url}) ${repoInfo.private ? '🔒' : ''}\n\n` +
      //     `${repoInfo.description || ""}\n\n` +
      //     `<div class="repo-stats">\n` +
      //     `[![](https://img.shields.io/badge/⭐_stars-${formatNumber(repoInfo.stargazers_count)}-yellow)](${repoInfo.html_url}/stargazers) ` +
      //     `[![](https://img.shields.io/badge/🍴_forks-${formatNumber(repoInfo.forks_count)}-blue)](${repoInfo.html_url}/network/members) ` +
      //     `[![](https://img.shields.io/badge/👁️_watchers-${formatNumber(repoInfo.watchers_count)}-lightgrey)](${repoInfo.html_url}/watchers)\n` +
      //     `</div>\n\n` +
      //     `${repoInfo.language ? `**Language:** \`${repoInfo.language}\` • ` : ""}` +
      //     `${repoInfo.license ? `**License:** ${repoInfo.license.name} • ` : ""}` +
      //     `**Updated:** ${formatDate(repoInfo.updated_at)}\n\n` +
      //     `${repoInfo.topics && repoInfo.topics.length > 0 ?
      //       `<div class="repo-topics">\n` +
      //       repoInfo.topics.map(topic => `\`${topic}\``).join(" ") +
      //       `\n</div>\n\n` : ""}` +
      //     `<div class="repo-link">\n` +
      //     `[View on GitHub](${repoInfo.html_url})\n` +
      //     `</div>\n` +
      //     `</div>`
      //     : ""
      //   }`,
      // },
      {
        id: "project_overview",
        title: `Overview`,
        content: tutorial.indexContent || "# Loading content...",
      },
      // Include all other chapters
      ...tutorial.chapters.map((chapter) => ({
        id: chapter.filename,
        title: chapter.filename
          .replace(/^\d+_|_/g, " ")
          .replace(/\.md$/, "")
          .trim()
          .split(" ")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" "),
        content: chapter.content || "# Loading content...",
      })),
    ],
  };

  return (
    <div className="w-full px-2 py-2 mx-auto">
      {/* Compact Header Section */}
      <div>
        {tutorial.imageSrc === "ss" ? (
          /* Compact Cover Image Header */
          <div className="relative w-full h-48 lg:h-56 rounded-xl overflow-hidden shadow-lg mb-4">
            <img
              src={tutorial.imageSrc}
              alt={`${tutorial.title} cover`}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                const container = target.closest(".relative");
                if (container) {
                  (container.parentElement as HTMLElement).style.display =
                    "none";
                }
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-black/10 to-transparent" />
            <div className="absolute bottom-4 left-6 right-6">
              <h1 className="text-white text-2xl lg:text-3xl font-bold mb-2 leading-tight">
                {tutorial.title}
              </h1>
              {tutorial.description && (
                <p className="text-white/90 text-sm lg:text-base leading-relaxed">
                  {tutorial.description}
                </p>
              )}
            </div>

            {/* Regenerate Cover Button - Positioned in top right */}
            <div className="absolute top-4 right-4">
              <RegenerateCoverButton
                tutorialId={tutorial.id.toString()}
                userId={tutorial.userId}
                onSuccess={handleCoverRegenerated}
              />
            </div>
          </div>
        ) : (
          /* Compact Text-based Header */
          <>
            {" "}
            <div className="fixed bottom-28 left-4 rounded-full p-4 shadow-lg z-50">
              <RegenerateCoverButton
                tutorialId={tutorial.id.toString()}
                userId={tutorial.userId}
                onSuccess={handleCoverRegenerated}
              />
            </div>
          </>
        )}

        {/* GitHub Repository Card */}
        {/* {tutorial.repoUrl && (
              <div className="max-w-5xl mx-auto mb-4">
                <GitHubRepoCard
                  repoUrl={tutorial.repoUrl}
                  className="shadow-lg border-0"
                />
              </div>
            )} */}
      </div>

      {/* Tutorial Content - Full Width */}
      <div className="w-full">
        <TutorialViewer tutorial={tutorialContent} />
      </div>
    </div>
  );
};

export default TutorialDetails;
