import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { useTrialStatus } from "@/hooks/useTrialStatus";
import { toast } from "@/hooks/use-toast";

const RepositoryInput = ({ onSubmit }: { onSubmit: (data: any) => void }) => {
  const [repoUrl, setRepoUrl] = useState("");
  const [includePattern, setIncludePattern] = useState("**/*.{js,jsx,ts,tsx}");
  const [excludePattern, setExcludePattern] = useState("node_modules/**");
  const [maxFileSize, setMaxFileSize] = useState(500); // KB
  const { trialStatus, loading } = useTrialStatus();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Check trial limits before submitting
    if (!loading && trialStatus.isInTrial && !trialStatus.canCreateTutorial) {
      toast({
        title: "Trial Limit Reached",
        description: `You've created ${trialStatus.tutorialsCreated} of ${trialStatus.tutorialsLimit} allowed tutorials. Upgrade to create more.`,
        variant: "destructive",
      });
      return;
    }

    onSubmit({
      repoUrl,
      includePattern,
      excludePattern,
      maxFileSize,
      language: "en",
      maxAbstractions: 10,
    });
  };

  const isSubmitDisabled = !repoUrl || (!loading && trialStatus.isInTrial && !trialStatus.canCreateTutorial);

  return (
    <Card className="w-full max-w-3xl animate-fade-in">
      <CardHeader>
        <CardTitle className="text-2xl">Generate Tutorial</CardTitle>
        <CardDescription>
          Enter a GitHub repository URL and configure parameters to generate a
          comprehensive tutorial
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!loading && trialStatus.isInTrial && !trialStatus.canCreateTutorial && (
          <Alert className="mb-6 border-orange-200 bg-orange-50">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              You've reached your trial limit of {trialStatus.tutorialsLimit} tutorials. 
              Upgrade your account to create unlimited tutorials.
            </AlertDescription>
          </Alert>
        )}
        
        <form onSubmit={handleSubmit}>
          <div className="grid w-full items-center gap-6">
            <div className="space-y-2">
              <Label htmlFor="repoUrl">GitHub Repository URL</Label>
              <Input
                id="repoUrl"
                placeholder="https://github.com/username/repository"
                value={repoUrl}
                onChange={(e) => setRepoUrl(e.target.value)}
                className="w-full"
                required
              />
            </div>

            <Tabs defaultValue="patterns" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="patterns">Patterns</TabsTrigger>
                <TabsTrigger value="limits">Limits</TabsTrigger>
                <TabsTrigger value="advanced">Advanced</TabsTrigger>
              </TabsList>
              <TabsContent value="patterns" className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="includePattern">Include Patterns</Label>
                  <Input
                    id="includePattern"
                    placeholder="**/*.{js,jsx,ts,tsx}"
                    value={includePattern}
                    onChange={(e) => setIncludePattern(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="excludePattern">Exclude Patterns</Label>
                  <Input
                    id="excludePattern"
                    placeholder="node_modules/**"
                    value={excludePattern}
                    onChange={(e) => setExcludePattern(e.target.value)}
                  />
                </div>
              </TabsContent>
              <TabsContent value="limits" className="space-y-4 py-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="maxFileSize">Max File Size (KB)</Label>
                      <span className="text-sm text-muted-foreground">{maxFileSize} KB</span>
                    </div>
                    <Slider
                      id="maxFileSize"
                      min={10}
                      max={2000}
                      step={10}
                      value={[maxFileSize]}
                      onValueChange={(value) => setMaxFileSize(value[0])}
                    />
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="advanced" className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label>Language</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="outline" className="w-full" type="button">English</Button>
                    <Button variant="outline" className="w-full" disabled type="button">More coming soon</Button>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="maxAbstractions">Max Abstractions</Label>
                    <span className="text-sm text-muted-foreground">10</span>
                  </div>
                  <Slider
                    id="maxAbstractions"
                    min={5}
                    max={15}
                    step={1}
                    defaultValue={[10]}
                  />
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">Reset</Button>
        <Button 
          className="tutorial-gradient"
          onClick={handleSubmit}
          disabled={isSubmitDisabled}
        >
          {isSubmitDisabled && trialStatus.isInTrial && !trialStatus.canCreateTutorial 
            ? "Upgrade to Continue" 
            : "Generate Tutorial"
          }
        </Button>
      </CardFooter>
    </Card>
  );
};

export default RepositoryInput;
