import { SharedStore } from "../types"
import { Node } from '../../../pocketflow'
import { fetch_selected_github_files } from "../utils/crawl_github_files";
import { fetch_selected_local_files } from "../utils/crawl_local_files";
import { emitProgress, emitGraphStatus } from "../utils/events";




export class FetchRepo extends Node<SharedStore> {
  async prep(shared: SharedStore): Promise<string> {
    // Emit graph status to indicate this node is starting
    emitGraphStatus("FetchRepo", 0, "Preparing to fetch repository files");

    if(!shared.project_name && shared.repo_url) {
       const parts = shared.repo_url.split("/");
       const lastPart = parts[parts.length - 1];
       shared.project_name = lastPart.replace(".git", "");
    } else if(!shared.project_name && shared.local_dir)  {

      shared.project_name = shared.local_dir.split(/[\\/\\]/).pop();
    }else{
      shared.project_name = "Local Project";
    }

    console.log('Project Name: ', shared.project_name)
    const tutorialId = `${ shared.project_name.replace(/[^a-zA-Z0-9-_]/g, '-')}-${Date.now()}`;
    shared.tutorial_id = tutorialId;
    console.log('Tutorial Id: ', shared.tutorial_id)
    emitGraphStatus("FetchRepo", 10, `Preparing to fetch files for ${shared.project_name}`);
  
    return JSON.stringify({
      repo_url: shared.repo_url,
      local_dir: shared.local_dir,
      token: shared.github_token,
      selected_files: shared.selected_files || [],
      use_relative_paths: true
    })

  }

  async exec(args: string): Promise<string> {
    // Parse the JSON string into an object
    const parsedArgs = JSON.parse(args);
    console.log("Args", parsedArgs);

    // Update graph status to indicate execution has started
    emitGraphStatus("FetchRepo", 20, "Starting file retrieval");

    let files: any;
    if(parsedArgs.repo_url) {
        console.log("Fetching selected files from repository..", parsedArgs.repo_url);
        emitGraphStatus("FetchRepo", 30, `Fetching ${parsedArgs.selected_files.length} selected files from GitHub repository`);

        files = await fetch_selected_github_files(
          parsedArgs.repo_url,
          parsedArgs.selected_files,
          {
            githubToken: parsedArgs.token,
            useRelativePaths: parsedArgs.use_relative_paths,
          }
        );

        emitGraphStatus("FetchRepo", 60, "GitHub repository files retrieved");

    } else if(parsedArgs.local_dir) {
        emitGraphStatus("FetchRepo", 30, `Fetching ${parsedArgs.selected_files.length} selected files from local directory`);

        files = await fetch_selected_local_files(
          parsedArgs.local_dir,
          parsedArgs.selected_files,
          {
            useRelativePaths: parsedArgs.use_relative_paths,
          }
        );

        emitGraphStatus("FetchRepo", 60, "Local directory files retrieved");
    }
    
    const files_list = Object.entries(files.files || {});
    if (files_list.length === 0) {
      emitGraphStatus("FetchRepo", 70, "Error: No files found");
      throw new Error("Failed to fetch files");
    }

    console.log(`Fetched ${files_list.length} files.`);
    emitGraphStatus("FetchRepo", 80, `Retrieved ${files_list.length} files`);
    return JSON.stringify(files_list);
  }

  async post(
    shared: SharedStore,
    _: string,
    execRes: string,
  ): Promise<string | undefined> {
    // Store the files in shared
    if (execRes && execRes !== "[]") {
      shared.files = JSON.parse(execRes);
    }

    // Emit progress event
    emitProgress("Repository Fetching", 20, `Fetched ${shared.files?.length || 0} files`);

    // Emit final graph status for this node
    emitGraphStatus("FetchRepo", 100, `Completed fetching ${shared.files?.length || 0} files`);

    return undefined;
  }
}
