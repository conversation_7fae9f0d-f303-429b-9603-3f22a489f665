
import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';
import { supabase } from '@/integrations/supabase/client';
import { toast } from './use-toast';

type UserSettings = {
  github: string;
  max_file_size: number;
  default_llm: string;
};

type UserDetails = {
  trial_start_date: string | null;
  trial_end_date: string | null;
  tutorials_created_count: number;
  trial_tutorials_limit: number;
  tier: string;
};

export const useUserSettings = () => {
  const { user } = useAuth();
  const [settings, setSettings] = useState<UserSettings>({
    github: '',
    max_file_size: 100,
    default_llm: ''
  });
  const [userDetails, setUserDetails] = useState<UserDetails>({
    trial_start_date: null,
    trial_end_date: null,
    tutorials_created_count: 0,
    trial_tutorials_limit: 5,
    tier: 'Spark'
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (user) {
      fetchUserSettings();
    }
  }, [user]);

  const fetchUserSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('user_details')
        .select('github, max_file_size, default_llm, trial_start_date, trial_end_date, tutorials_created_trial_count, trial_tutorials_limit, tier')
        .eq('id', user?.id)
        .maybeSingle();

      if (error) throw error;

      if (data) {
        setSettings({
          github: data.github || '',
          max_file_size: data.max_file_size || 100,
          default_llm: data.default_llm || ''
        });
        
        setUserDetails({
          trial_start_date: data.trial_start_date,
          trial_end_date: data.trial_end_date,
          tutorials_created_count: data.tutorials_created_trial_count || 0,
          trial_tutorials_limit: data.trial_tutorials_limit || 5,
          tier: data.tier || 'Spark'
        });
      }
    } catch (error) {
      console.error('Error fetching user settings:', error);
      toast({
        title: "Error",
        description: "Failed to load user settings",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async (newSettings: UserSettings) => {
    if (!user) return;
    
    setSaving(true);
    try {
      const { error } = await supabase
        .from('user_details')
        .update({
          github: newSettings.github || null,
          max_file_size: newSettings.max_file_size,
          default_llm: newSettings.default_llm || null
        })
        .eq('id', user.id);

      if (error) throw error;

      setSettings(newSettings);
      toast({
        title: "Success",
        description: "Settings saved successfully!",
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "Error",
        description: "Failed to save settings",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  return {
    settings,
    setSettings,
    saveSettings,
    userDetails,
    loading,
    saving
  };
};
