
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    
    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

    // Get the webhook payload
    const payload = await req.json();
    
    console.log('Auth webhook received:', payload.type);
    console.log('User data:', JSON.stringify(payload.record, null, 2));

    const { type, record } = payload;

    // Handle user created event
    if (type === 'INSERT') {
      const userData = {
        id: record.id,
        email: record.email || null,
        first_name: record.raw_user_meta_data?.first_name || null,
        last_name: record.raw_user_meta_data?.last_name || null,
        user_name: record.raw_user_meta_data?.username || record.email?.split('@')[0] || `user_${Date.now()}`,
        image_url: record.raw_user_meta_data?.avatar_url || null,
      };

      console.log('Inserting user details:', userData);

      const { error } = await supabase
        .from('user_details')
        .insert(userData);

      if (error) {
        console.error('Error inserting user details:', error);
        return new Response(
          JSON.stringify({ error: 'Failed to insert user details' }),
          { 
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }

      console.log('User details inserted successfully');
    }

    return new Response(
      JSON.stringify({ success: true }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Webhook error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
