import { Flow, Node } from '../pocketflow';
import {generateDiagram} from '../pocketflow/utils/diagramGenerator';

// Define some example nodes
class StartNode extends Node {
  async exec(): Promise<string> {
    return "Hello from StartNode";
  }
  
  async post(shared: any, prepRes: unknown, execRes: unknown): Promise<string> {
    return Math.random() > 0.5 ? "path1" : "path2";
  }
}

class ProcessNode extends Node {
  async exec(): Promise<string> {
    return "Processing data";
  }
}

class EndNode extends Node {
  async exec(): Promise<string> {
    return "End of flow";
  }
}

// Create a flow with branching
const startNode = new StartNode();
const processNode1 = new ProcessNode();
const processNode2 = new ProcessNode();
const endNode = new EndNode();

startNode.on("path1", processNode1);
startNode.on("path2", processNode2);
processNode1.next(endNode);
processNode2.next(endNode);

const flow = new Flow(startNode);

// Generate diagram
async function main() {
  await generateDiagram(flow, './output/flow-diagram.png', {
    width: 1200,
    height: 800,
    backgroundColor: '#f5f5f5',
    nodeColor: '#e1f5fe',
    fontSize: 14
  });
  
  console.log('Diagram generated successfully!');
}

main().catch(console.error);