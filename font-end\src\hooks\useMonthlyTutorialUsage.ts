
import { useAuth } from '@/hooks/useAuth';

interface MonthlyUsage {
  monthlyTutorialsCreated: number;
  maxTutorialsPerMonth: number;
  currentBillingCycleStart: string | undefined;
  currentBillingCycleEnd: string | undefined;
  loading: boolean;
  error: string | null;
}

export const useMonthlyTutorialUsage = (): MonthlyUsage => {
  const { subscriptionInfo, loading } = useAuth();
  
  return {
    monthlyTutorialsCreated: subscriptionInfo.monthlyTutorialsCreated,
    maxTutorialsPerMonth: subscriptionInfo.maxTutorialsPerMonth,
    currentBillingCycleStart: subscriptionInfo.currentBillingCycleStart || null,
    currentBillingCycleEnd: subscriptionInfo.currentBillingCycleEnd || null,
    loading,
    error: null,
  };
};
