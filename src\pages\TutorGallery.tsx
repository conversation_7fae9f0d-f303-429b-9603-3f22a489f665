import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useSession } from "@supabase/auth-helpers-react";
import { BookOpen, Clock, Target, Users, Zap, ExternalLink, Search, Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

interface Tutorial {
  tutorial_id: string;
  project_name: string;
  description: string;
  repo_url: string;
  language: string;
  target_audience: string;
  estimated_time: number;
  learning_objectives: string[];
  prerequisites: string[];
  created_at: string;
  index_url: string;
  tutorial_type: string;
}

const TutorGallery = () => {
  const session = useSession();
  const [tutorials, setTutorials] = useState<Tutorial[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterAudience, setFilterAudience] = useState<string>("all");
  const [filterLanguage, setFilterLanguage] = useState<string>("all");

  useEffect(() => {
    fetchTutorials();
  }, [session]);

  const fetchTutorials = async () => {
    try {
      setLoading(true);
      
      let query = supabase
        .from('tutorial_metadata')
        .select('*')
        .eq('tutorial_type', 'interactive_tutorial')
        .order('created_at', { ascending: false });

      if (session?.user?.id) {
        query = query.eq('user_id', session.user.id);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching tutorials:', error);
        return;
      }

      setTutorials(data || []);
    } catch (error) {
      console.error('Error fetching tutorials:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredTutorials = tutorials.filter(tutorial => {
    const matchesSearch = tutorial.project_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tutorial.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesAudience = filterAudience === "all" || tutorial.target_audience === filterAudience;
    const matchesLanguage = filterLanguage === "all" || tutorial.language === filterLanguage;

    return matchesSearch && matchesAudience && matchesLanguage;
  });

  const getAudienceIcon = (audience: string) => {
    switch (audience) {
      case "beginner":
        return <Users className="h-4 w-4 text-green-600" />;
      case "intermediate":
        return <Target className="h-4 w-4 text-yellow-600" />;
      case "advanced":
        return <Zap className="h-4 w-4 text-red-600" />;
      default:
        return <Users className="h-4 w-4 text-gray-600" />;
    }
  };

  const getAudienceColor = (audience: string) => {
    switch (audience) {
      case "beginner":
        return "bg-green-100 text-green-800 border-green-200";
      case "intermediate":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "advanced":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const uniqueLanguages = [...new Set(tutorials.map(t => t.language))];

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto space-y-8">
        <Skeleton className="h-12 w-64" />
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Skeleton key={i} className="h-64 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <BookOpen className="h-8 w-8 text-blue-600" />
          <h1 className="text-4xl font-bold text-gray-900">Interactive Tutorial Gallery</h1>
        </div>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Browse your collection of interactive tutorials with hands-on exercises and learning activities.
        </p>
      </div>

      {/* Actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <Link to="/dashboard/create-tutor">
          <Button className="bg-blue-600 hover:bg-blue-700 text-white">
            <BookOpen className="h-4 w-4 mr-2" />
            Create New Tutorial
          </Button>
        </Link>

        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4 w-full sm:w-auto">
          {/* Search */}
          <div className="relative">
            <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search tutorials..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full sm:w-64"
            />
          </div>

          {/* Filters */}
          <select
            value={filterAudience}
            onChange={(e) => setFilterAudience(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Audiences</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>

          <select
            value={filterLanguage}
            onChange={(e) => setFilterLanguage(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Languages</option>
            {uniqueLanguages.map(lang => (
              <option key={lang} value={lang}>{lang}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200 text-center">
          <div className="text-2xl font-bold text-blue-600">{tutorials.length}</div>
          <div className="text-sm text-gray-600">Total Tutorials</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200 text-center">
          <div className="text-2xl font-bold text-green-600">
            {tutorials.filter(t => t.target_audience === 'beginner').length}
          </div>
          <div className="text-sm text-gray-600">Beginner</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200 text-center">
          <div className="text-2xl font-bold text-yellow-600">
            {tutorials.filter(t => t.target_audience === 'intermediate').length}
          </div>
          <div className="text-sm text-gray-600">Intermediate</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200 text-center">
          <div className="text-2xl font-bold text-red-600">
            {tutorials.filter(t => t.target_audience === 'advanced').length}
          </div>
          <div className="text-sm text-gray-600">Advanced</div>
        </div>
      </div>

      {/* Tutorials Grid */}
      {filteredTutorials.length === 0 ? (
        <div className="text-center py-12">
          <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            {tutorials.length === 0 ? "No tutorials yet" : "No tutorials match your filters"}
          </h3>
          <p className="text-gray-600 mb-6">
            {tutorials.length === 0 
              ? "Create your first interactive tutorial to get started."
              : "Try adjusting your search or filter criteria."
            }
          </p>
          {tutorials.length === 0 && (
            <Link to="/dashboard/create-tutor">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                <BookOpen className="h-4 w-4 mr-2" />
                Create Your First Tutorial
              </Button>
            </Link>
          )}
        </div>
      ) : (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTutorials.map((tutorial) => (
            <div
              key={tutorial.tutorial_id}
              className="bg-white rounded-lg border border-gray-200 hover:shadow-lg transition-shadow duration-200"
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <BookOpen className="h-5 w-5 text-blue-600" />
                    <h3 className="font-semibold text-gray-900 truncate">
                      {tutorial.project_name}
                    </h3>
                  </div>
                  <Badge className={`${getAudienceColor(tutorial.target_audience)} text-xs`}>
                    <div className="flex items-center space-x-1">
                      {getAudienceIcon(tutorial.target_audience)}
                      <span className="capitalize">{tutorial.target_audience}</span>
                    </div>
                  </Badge>
                </div>

                {/* Description */}
                <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                  {tutorial.description}
                </p>

                {/* Learning Objectives */}
                {tutorial.learning_objectives && tutorial.learning_objectives.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-xs font-medium text-gray-700 mb-2">Learning Objectives:</h4>
                    <ul className="text-xs text-gray-600 space-y-1">
                      {tutorial.learning_objectives.slice(0, 2).map((objective, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-blue-600 mr-1">•</span>
                          <span className="line-clamp-1">{objective}</span>
                        </li>
                      ))}
                      {tutorial.learning_objectives.length > 2 && (
                        <li className="text-gray-500">
                          +{tutorial.learning_objectives.length - 2} more...
                        </li>
                      )}
                    </ul>
                  </div>
                )}

                {/* Metadata */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-xs text-gray-500">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>{tutorial.estimated_time || 60} minutes</span>
                    <span className="mx-2">•</span>
                    <span className="capitalize">{tutorial.language}</span>
                  </div>
                  
                  <div className="text-xs text-gray-500">
                    Created {formatDate(tutorial.created_at)}
                  </div>
                </div>

                {/* Prerequisites */}
                {tutorial.prerequisites && tutorial.prerequisites.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-xs font-medium text-gray-700 mb-1">Prerequisites:</h4>
                    <div className="flex flex-wrap gap-1">
                      {tutorial.prerequisites.slice(0, 2).map((prereq, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {prereq}
                        </Badge>
                      ))}
                      {tutorial.prerequisites.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{tutorial.prerequisites.length - 2}
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex space-x-2">
                  <Link
                    to={`/tutorial/${tutorial.tutorial_id}`}
                    className="flex-1"
                  >
                    <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm">
                      <BookOpen className="h-3 w-3 mr-1" />
                      Start Learning
                    </Button>
                  </Link>
                  
                  {tutorial.repo_url && (
                    <a
                      href={tutorial.repo_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                      title="View Repository"
                    >
                      <ExternalLink className="h-4 w-4 text-gray-600" />
                    </a>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Load More (if needed) */}
      {filteredTutorials.length > 0 && filteredTutorials.length < tutorials.length && (
        <div className="text-center">
          <Button variant="outline" onClick={fetchTutorials}>
            Load More Tutorials
          </Button>
        </div>
      )}
    </div>
  );
};

export default TutorGallery;
