const FeaturesPreview = () => {
  const features = [{
    icon: "🤖",
    title: "Agentic AI Framework",
    description: "Our innovative AI workflow orchestration transforms complex codebases into structured learning experiences."
  }, {
    icon: "📖",
    title: "Comprehensive Tutorials",
    description: "Generate step-by-step tutorials that break down complex concepts into digestible learning modules."
  }, {
    icon: "🔗",
    title: "GitHub Integration",
    description: "Seamlessly connect any GitHub repository and instantly generate tutorials from existing codebases."
  }, {
    icon: "🎯",
    title: "Beginner-Focused",
    description: "Automatically adjust content complexity to match different skill levels, perfect for new developers."
  }, {
    icon: "⚡",
    title: "Rapid Generation",
    description: "Transform repositories into complete tutorial series in minutes, not hours or days."
  }, {
    icon: "📊",
    title: "Learning Analytics",
    description: "Track progress and understanding with built-in analytics and assessment tools."
  }];
  return <div id="features" className="bg-white py-20">
      <div className="container mx-auto px-6">
        <header className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Revolutionizing <strong>Code Education</strong> with
            <span className="text-gray-700"> AI Innovation</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            CodeTutorPro leverages cutting-edge <strong>AI technology</strong> to automatically transform any <strong>GitHub codebase</strong>
            into comprehensive, <strong>beginner-friendly programming tutorials</strong> and learning experiences.
          </p>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => <div key={index} className="bg-white rounded-2xl p-8 border border-gray-200 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 animate-slide-up" style={{
          animationDelay: `${index * 0.1}s`
        }}>
              <div className="text-4xl mb-4 animate-float" style={{
            animationDelay: `${index * 0.2}s`
          }}>
                {feature.icon}
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </div>)}
        </div>

        <div className="mt-16 text-center">
          <div className="bg-gray-50 rounded-2xl p-8 max-w-4xl mx-auto border border-gray-200">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Transform Your Learning Experience?</h3>
            <p className="text-gray-700 mb-6">
              Join thousands of developers who are already on the waiting list for early access to CodeTutorPro.
            </p>
            <div className="flex justify-center gap-4 text-sm text-gray-600">
              <span className="bg-white px-4 py-2 rounded-full border border-gray-200">✨ Free Early Access</span>
              <span className="bg-white px-4 py-2 rounded-full border border-gray-200">🚀 Launch in Q2 2025</span>
              <span className="bg-white px-4 py-2 rounded-full border border-gray-200">🎯 Beginner-Friendly</span>
            </div>
          </div>
        </div>
      </div>
    </div>;
};
export default FeaturesPreview;