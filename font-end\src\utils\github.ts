


// Parse GitHub URL to get owner and repo
export const parseGitHubUrl = (url: string) => {
    try {
      const regex = /github\.com\/([^\/]+)\/([^\/]+)/;
      const match = url.match(regex);
      if (match && match.length >= 3) {
        return {
          owner: match[1],
          repo: match[2].replace(".git", "").split("?")[0].split("#")[0],
        };
      }
      return null;
    } catch {
      return null;
    }
  };