import yaml from 'js-yaml';
import { Node } from '../../../pocketflow';
import { SharedStore } from '../types';

import { emitGraphStatus, emitProgress } from "../utils/events";
import { callLlm_openrouter } from '@/Agents/shared/callLlm_openrouter';
import { buildPrompt } from "../../../pocketflow/utils/buildPrompt";
import { ORDER_CHAPTERS_PROMPT } from "../prompts/orderChapters";

export class OrderChapters extends Node<SharedStore> {
  private curRetry = 0;

  async prep(shared: SharedStore) {
    // Emit graph status to indicate this node is starting
    emitGraphStatus("OrderChapters", 0, "Starting chapter ordering");

    const abstractions = shared.abstractions || [];
    const relationships = shared.relationships!;
    const projectName = shared.project_name!;
    const language = shared.language || 'english';
    const useCache = shared.use_cache ?? true;

    emitGraphStatus("OrderChapters", 10, `Preparing to order ${abstractions.length} chapters`);

    // Prepare context for the LLM
    const abstractionInfoForPrompt: string[] = [];
    for (let i = 0; i < abstractions.length; i++) {
      abstractionInfoForPrompt.push(`- ${i} # ${abstractions[i].name}`);
    }
    const abstractionListing = abstractionInfoForPrompt.join('\n');

    // Use potentially translated summary and labels
    let summaryNote = '';
    if (language.toLowerCase() !== 'english') {
      summaryNote = ` (Note: Project Summary might be in ${language.charAt(0).toUpperCase() + language.slice(1)})`;
    }

    emitGraphStatus("OrderChapters", 20, "Building context from project summary and relationships");

    let context = `Project Summary${summaryNote}:\n${relationships.summary}\n\n`;
    context += 'Relationships (Indices refer to abstractions above):\n';
    for (const rel of relationships.details) {
      const fromName = abstractions[rel.from].name;
      const toName = abstractions[rel.to].name;
      context += `- From ${rel.from} (${fromName}) to ${rel.to} (${toName}): ${rel.label}\n`;
    }

    let listLangNote = '';
    if (language.toLowerCase() !== 'english') {
      listLangNote = ` (Names might be in ${language.charAt(0).toUpperCase() + language.slice(1)})`;
    }

    emitGraphStatus("OrderChapters", 30, "Preparation complete, ready for chapter ordering");

    return {
      abstractionListing,
      context,
      numAbstractions: abstractions.length,
      projectName,
      listLangNote,
      useCache,
      user_id: shared.user_id,
      session_id: shared.session_id,
      tutorial_id: shared.tutorial_id
    };
  }

  async exec(prepRes: any): Promise<number[]> {
    const {
      abstractionListing,
      context,
      numAbstractions,
      projectName,
      listLangNote,
      useCache,
      user_id,
      tutorial_id
    } = prepRes;

    emitGraphStatus("OrderChapters", 40, "Starting LLM analysis to determine optimal chapter order");
    console.log("Determining chapter order using LLM...");

    const prompt = buildPrompt(ORDER_CHAPTERS_PROMPT, {
      project_name: projectName,
      list_lang_note: listLangNote,
      abstraction_listing: abstractionListing,
      context
    });

    emitGraphStatus("OrderChapters", 50, "Sending request to LLM for chapter ordering");
    const response = await callLlm_openrouter ({
      tutorial_id,
      prompt,
      
      
      use_cache: useCache && this.curRetry === 0,
      user_id
    
    }
    );
    //const response = await callLlm( prompt, useCache && this.curRetry === 0);
    emitGraphStatus("OrderChapters", 60, "Received response from LLM, processing results");

    // --- Validation ---
    const yamlMatch = response.trim().match(/```yaml([\s\S]*?)```/);
    if (!yamlMatch) {
      emitGraphStatus("OrderChapters", 65, "Error: LLM output does not contain YAML block");
      throw new Error('LLM output does not contain YAML block');
    }

    const yamlStr = yamlMatch[1].trim();
    emitGraphStatus("OrderChapters", 70, "Parsing YAML response");
    const orderedIndicesRaw = yaml.load(yamlStr) as any[];

    if (!Array.isArray(orderedIndicesRaw)) {
      emitGraphStatus("OrderChapters", 75, "Error: LLM output is not a list");
      throw new Error('LLM output is not a list');
    }

    emitGraphStatus("OrderChapters", 80, "Validating chapter order");
    const orderedIndices: number[] = [];
    const seenIndices = new Set<number>();

    for (const entry of orderedIndicesRaw) {
      try {
        let idx: number;

        if (typeof entry === 'number') {
          idx = entry;
        } else if (typeof entry === 'string' && entry.includes('#')) {
          idx = parseInt(entry.split('#')[0].trim(), 10);
        } else {
          idx = parseInt(String(entry).trim(), 10);
        }

        if (!(0 <= idx && idx < numAbstractions)) {
          emitGraphStatus("OrderChapters", 82, `Error: Invalid index ${idx} in ordered list`);
          throw new Error(`Invalid index ${idx} in ordered list. Max index is ${numAbstractions-1}.`);
        }

        if (seenIndices.has(idx)) {
          emitGraphStatus("OrderChapters", 83, `Error: Duplicate index ${idx} found in ordered list`);
          throw new Error(`Duplicate index ${idx} found in ordered list.`);
        }

        orderedIndices.push(idx);
        seenIndices.add(idx);
      } catch (error) {
        emitGraphStatus("OrderChapters", 84, `Error: Could not parse index from ordered list entry: ${entry}`);
        throw new Error(`Could not parse index from ordered list entry: ${entry}`);
      }
    }

    // Check if all abstractions are included
    if (orderedIndices.length !== numAbstractions) {
      const missingIndices = [...Array(numAbstractions).keys()]
        .filter(i => !seenIndices.has(i));
      emitGraphStatus("OrderChapters", 85, `Error: Missing indices in ordered list: ${missingIndices}`);
      throw new Error(
        `Ordered list length (${orderedIndices.length}) does not match number of abstractions (${numAbstractions}). Missing indices: ${missingIndices}`
      );
    }

    emitGraphStatus("OrderChapters", 90, `Successfully ordered ${orderedIndices.length} chapters`);
    console.log(`Determined chapter order (indices): ${orderedIndices}`);
    return orderedIndices;
  }

  async post(
    shared: SharedStore,
    _: any,
    orderedIndices: number[]
  ): Promise<string | undefined> {
    emitGraphStatus("OrderChapters", 95, `Storing chapter order in shared store`);
    shared.chapter_order = orderedIndices;

    // Emit progress event
    emitProgress("Chapter Ordering", 70, `Determined optimal order for ${orderedIndices.length} chapters`);

    // Final graph status
    emitGraphStatus("OrderChapters", 100, "Chapter ordering complete");

    return 'default';
  }
}