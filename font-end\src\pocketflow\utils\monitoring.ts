import { BaseNode, Flow, Node } from '../index';

/**
 * Metrics collected for node execution
 */
export interface NodeMetrics {
  nodeName: string;
  startTime: number;
  endTime: number;
  duration: number;
  retryCount: number;
  action?: string;
  error?: Error;
}

/**
 * Utility class for monitoring node and flow execution
 * Collects performance metrics and execution paths
 */
export class NodeMonitor<S = unknown> {
  private metrics: NodeMetrics[] = [];
  private originalNode: BaseNode;
  private monitoredNode: BaseNode;

  /**
   * Creates a monitored version of a node
   * @param node The node to monitor
   */
  constructor(node: BaseNode) {
    this.originalNode = node;
    this.monitoredNode = this.wrapNode(node);
  }

  /**
   * Wraps a node with monitoring capabilities
   * @param node Node to wrap
   * @returns Monitored node
   */
  private wrapNode(node: BaseNode): BaseNode {
    const wrapped = node.clone();
    
    // Override the _run method to add monitoring
    const originalRun = wrapped._run;
    wrapped._run = async function(shared: S): Promise<string | undefined> {
      const startTime = Date.now();
      const nodeName = this.constructor.name;
      const metric: NodeMetrics = {
        nodeName,
        startTime,
        endTime: 0,
        duration: 0,
        retryCount: 'currentRetry' in this ? this.currentRetry : 0
      };
      
      try {
        const action = await originalRun.call(this, shared);
        metric.action = action;
        return action;
      } catch (error) {
        metric.error = error as Error;
        throw error;
      } finally {
        metric.endTime = Date.now();
        metric.duration = metric.endTime - metric.startTime;
        this.metrics.push(metric);
      }
    };
    
    return wrapped;
  }

  /**
   * Runs the monitored node
   * @param shared Shared state
   * @returns Action from node execution
   */
  async run(shared: S): Promise<string | undefined> {
    this.metrics = [];
    return await this.monitoredNode.run(shared);
  }

  /**
   * Gets the collected metrics
   * @returns Array of execution metrics
   */
  getMetrics(): NodeMetrics[] {
    return [...this.metrics];
  }

  /**
   * Gets the total execution time
   * @returns Total duration in milliseconds
   */
  getTotalDuration(): number {
    return this.metrics.reduce((sum, m) => sum + m.duration, 0);
  }

  /**
   * Gets the original unwrapped node
   * @returns Original node
   */
  getOriginalNode(): BaseNode {
    return this.originalNode;
  }
}

/**
 * Utility class for monitoring flow execution
 * Tracks the complete execution path and performance
 */
export class FlowMonitor<S = unknown> {
  private metrics: NodeMetrics[] = [];
  private flow: Flow<S>;

  /**
   * Creates a monitored version of a flow
   * @param flow The flow to monitor
   */
  constructor(flow: Flow<S>) {
    this.flow = flow;
  }

  /**
   * Runs the flow with monitoring
   * @param shared Shared state
   * @returns Result of flow execution
   */
  async run(shared: S): Promise<string | undefined> {
    this.metrics = [];
    
    // Create a proxy to intercept node execution
    const originalOrchestrate = this.flow['_orchestrate'];
    this.flow['_orchestrate'] = async (shared: S, params?: any): Promise<void> => {
      let current: BaseNode | undefined = this.flow.start.clone();
      const p = params || this.flow['_params'];
      
      while (current) {
        const startTime = Date.now();
        const nodeName = current.constructor.name;
        const metric: NodeMetrics = {
          nodeName,
          startTime,
          endTime: 0,
          duration: 0,
          retryCount: 'currentRetry' in current ? (current as Node).currentRetry : 0
        };
        
        try {
          current.setParams(p);
          const action = await current._run(shared);
          metric.action = action;
          current = current.getNextNode(action);
          current = current?.clone();
        } catch (error) {
          metric.error = error as Error;
          throw error;
        } finally {
          metric.endTime = Date.now();
          metric.duration = metric.endTime - metric.startTime;
          this.metrics.push(metric);
        }
      }
    };
    
    try {
      return await this.flow.run(shared);
    } finally {
      // Restore original method
      this.flow['_orchestrate'] = originalOrchestrate;
    }
  }

  /**
   * Gets the collected metrics
   * @returns Array of execution metrics
   */
  getMetrics(): NodeMetrics[] {
    return [...this.metrics];
  }

  /**
   * Gets the execution path as node names
   * @returns Array of node names in execution order
   */
  getExecutionPath(): string[] {
    return this.metrics.map(m => m.nodeName);
  }

  /**
   * Gets the total execution time
   * @returns Total duration in milliseconds
   */
  getTotalDuration(): number {
    return this.metrics.reduce((sum, m) => sum + m.duration, 0);
  }

  /**
   * Prints a summary of the execution
   */
  printSummary(): void {
    console.log('=== Flow Execution Summary ===');
    console.log(`Total duration: ${this.getTotalDuration()}ms`);
    console.log(`Nodes executed: ${this.metrics.length}`);
    
    console.log('\nExecution path:');
    this.metrics.forEach((m, i) => {
      console.log(`${i+1}. ${m.nodeName} (${m.duration}ms) → ${m.action || 'END'}`);
    });
    
    const errors = this.metrics.filter(m => m.error);
    if (errors.length > 0) {
      console.log('\nErrors:');
      errors.forEach((m, i) => {
        console.log(`- ${m.nodeName}: ${m.error?.message}`);
      });
    }
  }
}

