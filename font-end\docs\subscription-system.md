# PocketFlow Subscription System Documentation


#
Invoke `create-checkout` to create the subscrition in stripe.
Return to /dashboard?success=true or /dashboard?success=false

## Edge Function create-checkout

```mermaid
flowchart TD
    A[Client Request] --> B{Is OPTIONS request?}
    B -- Yes --> C[Return 204 with CORS headers]
    B -- No --> D[Initialize Supabase Client]
    D --> E[Log: Function started]
    E --> F[Get Authorization header]
    F --> G[Extract token]
    G --> H[Get user from Supabase]
    H --> I{User authenticated?}
    I -- No --> Z[Return 500 Error: User not authenticated]
    I -- Yes --> J[Log: User authenticated]
    J --> K[Parse JSON body]
    K --> L{priceId && tierName present?}
    L -- No --> Y[Return 500 Error: Missing parameters]
    L -- Yes --> M[Log: Request parameters]
    M --> N[Query tier_settings from Supabase]
    N --> O{tierError?}
    O -- Yes --> P[Log: Error fetching tier settings]
    P --> Z
    O -- No --> Q[Log: Tier settings retrieved]
    Q --> R[Initialize Stripe Client]
    R --> S[List Stripe customers by email]
    S --> T{Existing customer found?}
    T -- Yes --> U[Set customerId from existing customer]
    U --> V[Log: Existing customer found]
    T -- No --> W[Log: No existing customer]
    V & W --> X[Prepare sessionConfig object]
    X --> A1{Has trial?}
    A1 -- Yes --> A2[Add trial_period_days to subscription_data]
    A2 --> B1[Log: Trial period added]
    A1 -- No --> B1
    B1 --> C1[Create Stripe checkout session]
    C1 --> D1[Log: Checkout session created]
    D1 --> E1[Return 200 with session URL]
    Z & Y --> F1[Return 500 with error message]
```
```mermaid
sequenceDiagram
    participant Client
    participant EdgeFunction as Edge Function
    participant Supabase
    participant Stripe

    Client->>EdgeFunction: HTTP Request
    Note right of EdgeFunction: Receive request and headers
    EdgeFunction->>EdgeFunction: Check if method == OPTIONS
    alt OPTIONS
        EdgeFunction-->>Client: Response 204 with CORS headers
    else Not OPTIONS
        EdgeFunction->>Supabase: auth.getUser(token)
        Supabase-->>EdgeFunction: user data
        EdgeFunction->>EdgeFunction: Validate user and email
        EdgeFunction->>EdgeFunction: Parse JSON body (priceId, tierName)
        EdgeFunction->>Supabase: from('tier_settings').select...where tier_name = tierName
        Supabase-->>EdgeFunction: tierSettings
        EdgeFunction->>Stripe: customers.list(email)
        Stripe-->>EdgeFunction: customers data
        EdgeFunction->>EdgeFunction: Determine customerId or mark new customer
        EdgeFunction->>Stripe: checkout.sessions.create(sessionConfig)
        Stripe-->>EdgeFunction: checkout session
        EdgeFunction-->>Client: Response 200 with session URL
    end
```

## 1. Subscription System Architecture

### Trial Management

```mermaid
flowchart TD
    A[User Signs Up] --> B[Default Trial Status Set]
    B --> C[Trial Period: 7 Days]
    C --> D{Trial Expired?}
    D -->|Yes| E[Downgrade to Free Tier]
    D -->|No| F[Continue Trial Access]
    F --> G{Trial Limit Reached?}
    G -->|Yes| H[Prompt for Upgrade]
    G -->|No| I[Continue Trial Usage]
```

- **Trial Initialization**: New users automatically receive a 7-day trial period
- **Trial Tracking**: 
  - `user_details.trial_end_date` tracks when trial expires
  - `user_details.trial_tutorials_limit` sets maximum tutorials during trial (default: 5)
  - `user_details.tutorials_created_count` tracks usage against limit
- **Trial Verification**: `is_user_in_trial` RPC function checks if user is within trial period
- **Usage Enforcement**: `can_user_create_tutorial` RPC function verifies if user can create more tutorials

### Subscription Renewal Process

```mermaid
flowchart TD
    A[Stripe Subscription] --> B[Billing Cycle Tracked]
    B --> C[Subscription End Date]
    C --> D{Renewal Time?}
    D -->|Yes| E[Stripe Automatic Renewal]
    E --> F[Update Subscription End Date]
    F --> G[Reset Monthly Quotas]
    D -->|No| H[Continue Current Cycle]
```

- **Billing Cycles**: Tracked in `user_details.current_billing_cycle_start/end`
- **Renewal Handling**: Managed by Stripe with subscription end dates stored in `subscribers.subscription_end`
- **Quota Reset**: `reset-monthly-counts` Edge Function resets quotas at billing cycle renewal

### Usage Limitations and Quota Enforcement

```mermaid
flowchart TD
    A[User Creates Tutorial] --> B{Check Subscription}
    B -->|Free Tier| C[Check Monthly Limit]
    B -->|Paid Tier| D[Check Tier Limit]
    C -->|Under Limit| E[Allow Creation]
    C -->|Over Limit| F[Block & Prompt Upgrade]
    D -->|Under Limit| E
    D -->|Over Limit| F
```

- **Tier-Based Limits**: Each subscription tier has different `max_tutorials_per_month` in `tier_settings`
- **Usage Tracking**: `user_details.monthly_tutorials_created` tracks current usage
- **Enforcement**: `can_user_create_tutorial` RPC function enforces limits based on subscription tier

## 2. Supabase Tables

### subscribers
- Tracks subscription status with Stripe
- **Key Fields**:
  - `email`: User email (primary key)
  - `user_id`: Reference to auth.users
  - `stripe_customer_id`: Stripe customer identifier
  - `subscribed`: Boolean indicating active subscription
  - `subscription_tier`: Current tier (Spark, Propel, Apex)
  - `subscription_end`: When subscription expires/renews

### user_details
- Stores user-specific subscription and usage data
- **Key Fields**:
  - `id`: User ID (primary key)
  - `tier`: Current subscription tier
  - `monthly_tutorials_created`: Count for current billing cycle
  - `trial_end_date`: When trial expires
  - `trial_tutorials_limit`: Maximum tutorials during trial
  - `tutorials_created_count`: Total tutorials created during trial
  - `current_billing_cycle_start`: Current billing period start
  - `current_billing_cycle_end`: Current billing period end
  - `stripe_subscription_id`: Active Stripe subscription ID

### tier_settings
- Defines features and limits for each subscription tier
- **Key Fields**:
  - `tier_name`: Tier identifier (Spark, Propel, Apex)
  - `price_monthly`: Monthly subscription cost
  - `max_tutorials_per_month`: Monthly tutorial creation limit
  - `max_file_size_mb`: Maximum repository size
  - `has_trial`: Whether tier offers trial
  - `trial_days`: Length of trial period
  - `features`: Array of tier-specific features

## 3. Edge Functions

### check-subscription
- **Purpose**: Verifies user's subscription status with Stripe
- **Flow**:
  1. Authenticates user from token
  2. Checks for Stripe customer
  3. Retrieves active subscriptions
  4. Determines subscription tier from price
  5. Updates `subscribers` table
  6. Updates billing cycle information
- **Returns**: Subscription status, tier, end date, trial status

### create-checkout
- **Purpose**: Creates Stripe checkout session for subscription
- **Flow**:
  1. Authenticates user
  2. Gets tier settings for selected plan
  3. Finds or creates Stripe customer
  4. Configures checkout session with trial if applicable
  5. Returns checkout URL

### customer-portal
- **Purpose**: Generates Stripe customer portal URL
- **Flow**:
  1. Authenticates user
  2. Retrieves Stripe customer ID
  3. Creates portal session
  4. Returns portal URL for subscription management

### reset-monthly-counts
- **Purpose**: Resets monthly tutorial quotas
- **Flow**:
  1. Processes users with Stripe subscriptions
  2. Checks if billing cycle has ended
  3. Updates billing cycle information
  4. Resets `monthly_tutorials_created` counter
  5. Handles free tier users with calendar month resets

### setup-monthly-reset-cron
- **Purpose**: Configures cron job for quota resets
- **Flow**:
  1. Sets up daily cron job at 2 AM UTC
  2. Triggers `reset-monthly-counts` function

## 4. System Flow Diagrams

### Subscription Lifecycle

```mermaid
stateDiagram-v2
    [*] --> FreeTrial
    FreeTrial --> FreeTier: Trial Expires
    FreeTrial --> PaidSubscription: Upgrade
    FreeTier --> PaidSubscription: Upgrade
    PaidSubscription --> FreeTier: Cancellation
    PaidSubscription --> PaidSubscription: Renewal
```

### Billing Cycle and Quota Reset

```mermaid
flowchart TD
    A[Daily Cron Job] --> B[reset-monthly-counts]
    B --> C{User Type?}
    C -->|Paid User| D[Check Stripe Billing Cycle]
    C -->|Free User| E[Check Calendar Month]
    D -->|Cycle Ended| F[Reset Quota]
    D -->|Cycle Active| G[No Action]
    E -->|Month Ended| F
    E -->|Month Active| G
    F --> H[Update Billing Cycle Dates]
```

### Component Relationship

```mermaid
flowchart TD
    A[Frontend Components] --> B[useSubscription Hook]
    A --> C[useTrialStatus Hook]
    A --> D[useMonthlyTutorialUsage Hook]
    B --> E[check-subscription]
    C --> F[is_user_in_trial RPC]
    C --> G[can_user_create_tutorial RPC]
    D --> H[user_details Table]
    E --> I[Stripe API]
    E --> J[subscribers Table]
    F --> H
    G --> H
    K[create-checkout] --> I
    L[customer-portal] --> I
    M[reset-monthly-counts] --> I
    M --> H
    M --> J
```

## 5. Potential Issues and Inconsistencies

1. **Trial Management Complexity**
   - Trial status is checked through multiple mechanisms (RPC functions and Edge Functions)
   - Potential for inconsistency between `subscribers.in_trial` and RPC `is_user_in_trial`

2. **Quota Reset Timing**
   - Free tier users reset based on calendar month
   - Paid users reset based on Stripe billing cycle
   - Could cause confusion for users transitioning between tiers

3. **Error Handling Gaps**
   - Limited error recovery in Edge Functions
   - Network failures during Stripe API calls could leave database in inconsistent state

4. **Billing Cycle Tracking**
   - Duplicate storage of billing information in both `subscribers` and `user_details`
   - Potential for data drift between tables

5. **Trial Limitations**
   - Trial limits are based on count rather than time for tutorial creation
   - Users might be confused by having both time-based trial and count-based limitations

6. **Subscription Tier Determination**
   - Tier is determined by price amount in `check-subscription`
   - Fragile approach if pricing changes, as it uses hardcoded price ranges

7. **Cron Job Reliability**
   - Single daily execution of quota reset
   - No retry mechanism if the function fails

8. **Data Synchronization**
   - No webhook handling for Stripe events like subscription updates/cancellations
   - Relies on periodic checks rather than event-driven updates