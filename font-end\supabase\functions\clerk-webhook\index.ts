

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    
    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

    // Get the webhook payload
    const payload = await req.json();
    
    console.log('Clerk webhook received:', payload.type);
    console.log('User data:', JSON.stringify(payload.data, null, 2));

    const { type, data } = payload;

    // Handle user created event
    if (type === 'user.created') {
      const userData = {
        id: data.id,
        clerk_user_id: data.id,
        email: data.email_addresses?.[0]?.email_address || null,
        first_name: data.first_name || null,
        last_name: data.last_name || null,
        image_url: data.image_url || null,
        user_name: data.username || data.email_addresses?.[0]?.email_address?.split('@')[0] || `user_${Date.now()}`,
      };

      console.log('Inserting user:', userData);

      const { error } = await supabase
        .from('user_details')
        .insert(userData);

      if (error) {
        console.error('Error inserting user:', error);
        return new Response(
          JSON.stringify({ error: 'Failed to insert user' }),
          { 
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }

      console.log('User inserted successfully');
    }

    // Handle user updated event
    if (type === 'user.updated') {
      const userData = {
        email: data.email_addresses?.[0]?.email_address || null,
        first_name: data.first_name || null,
        last_name: data.last_name || null,
        image_url: data.image_url || null,
        user_name: data.username || data.email_addresses?.[0]?.email_address?.split('@')[0] || `user_${Date.now()}`,
        updated_at: new Date().toISOString(),
      };

      console.log('Updating user:', data.id, userData);

      const { error } = await supabase
        .from('user_details')
        .update(userData)
        .eq('clerk_user_id', data.id);

      if (error) {
        console.error('Error updating user:', error);
        return new Response(
          JSON.stringify({ error: 'Failed to update user' }),
          { 
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }

      console.log('User updated successfully');
    }

    // Handle user deleted event
    if (type === 'user.deleted') {
      console.log('Setting user to inactive:', data.id);

      const { error } = await supabase
        .from('user_details')
        .update({ active: false, updated_at: new Date().toISOString() })
        .eq('clerk_user_id', data.id);

      if (error) {
        console.error('Error setting user to inactive:', error);
        return new Response(
          JSON.stringify({ error: 'Failed to set user to inactive' }),
          { 
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }

      console.log('User set to inactive successfully');
    }

    return new Response(
      JSON.stringify({ success: true }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Webhook error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

