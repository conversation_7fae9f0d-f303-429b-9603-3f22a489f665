import { Outlet } from "react-router-dom";
import TrialStatusBanner from "../TrialStatusBanner";
import NavBar from "./NavBar";
import ChatBotWrapper from "../ChatBotWrapper";
import { FooterDashboard } from "./FooterDashboard";

export function DashboardLayout() {
  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <NavBar />
      <TrialStatusBanner />
      <main className="container mx-auto px-4 py-8 md:py-12 flex-1">
        <Outlet /> {/* Content specific to the route will be rendered here */}
      </main>

      {/* Footer */}
      <FooterDashboard />
      {/* Chat Bot */}
      <ChatBotWrapper />
    </div>
  );
}
