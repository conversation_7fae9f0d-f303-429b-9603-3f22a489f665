
//https://supabase.com/docs/reference/javascript/typescript-support
const SUPABASE_URL = "https://axdtrqmggulirxskvwjg.supabase.co";

const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF4ZHRycW1nZ3VsaXJ4c2t2d2pnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY4MTQ2MzEsImV4cCI6MjA2MjM5MDYzMX0.K6tH_zUWz3gFOB9WStMmfDQY8y_jjyi9d-HB4tmSzho";


const SUPABASE_PUBLISHABLE_KEY_Ser = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF4ZHRycW1nZ3VsaXJ4c2t2d2pnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjgxNDYzMSwiZXhwIjoyMDYyMzkwNjMxfQ.K5cs1WF6A3m_-kBJw1x9fycMG80FTQMrL5lDDA8beUM";
import { createClient } from '@supabase/supabase-js'
// Create a single supabase client for interacting with your database
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY_Ser)


async function testBucket() {

// const { data, error } = await supabase
//   .storage
//   .getBucket('avatars')
       const { data, error } = await supabase
                .storage
                .createBucket('public', 
                    {
                        public: true,
                         allowedMimeTypes: ['image/png'],
                        fileSizeLimit: 1024, // 50MB limit
            });


            if (error) {
              console.error("Error creating bucket:", error);
            } else {
              console.log("Bucket created successfully:", data);
            }
    

}
// Run the tests
async function runTests() {
  await testBucket();

}

runTests().catch(console.error);


